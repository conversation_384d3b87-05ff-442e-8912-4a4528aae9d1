/**
 * 2025年ガソリン価格記事のJavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // スムーズスクロール
    setupSmoothScroll();
    
    // 目次のアクティブ状態管理
    setupTocHighlighting();
    
    // インタラクティブな価格計算機能
    setupPriceCalculator();
    
    // グラフ表示（Chart.jsを使用）
    setupCharts();
    
    // リージョンセレクタ機能
    setupRegionSelector();
    
    // サイドバーの固定
    setupStickyElement('.sidebar');
});

/**
 * スムーズスクロールの設定
 */
function setupSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                const offsetTop = targetElement.getBoundingClientRect().top + window.pageYOffset - 80;
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
                
                // URLハッシュを更新（履歴に追加せず）
                history.replaceState(null, null, targetId);
            }
        });
    });
}

/**
 * 目次の現在地ハイライト設定
 */
function setupTocHighlighting() {
    if (!document.querySelector('.toc-container')) return;
    
    const sections = document.querySelectorAll('h2[id], h3[id]');
    const tocLinks = document.querySelectorAll('.toc-container a');
    
    const observerOptions = {
        root: null,
        rootMargin: '-100px 0px -80% 0px',
        threshold: 0
    };
    
    const headingObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const activeId = entry.target.getAttribute('id');
                
                // 以前のアクティブリンクからクラスを削除
                tocLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === `#${activeId}`) {
                        link.classList.add('active');
                    }
                });
            }
        });
    }, observerOptions);
    
    sections.forEach(section => {
        headingObserver.observe(section);
    });
}

/**
 * 価格計算機能のセットアップ
 */
function setupPriceCalculator() {
    const calculators = document.querySelectorAll('.price-calculator');
    
    calculators.forEach(calculator => {
        const distanceInput = calculator.querySelector('input[name="distance"]');
        const fuelEfficiencyInput = calculator.querySelector('input[name="fuelEfficiency"]');
        const gasPriceInput = calculator.querySelector('input[name="gasPrice"]');
        const calculateButton = calculator.querySelector('.calculate-button');
        const resultElement = calculator.querySelector('.calculator-result');
        
        if (calculateButton && resultElement) {
            calculateButton.addEventListener('click', () => {
                // 入力値の取得
                const distance = parseFloat(distanceInput.value) || 0;
                const fuelEfficiency = parseFloat(fuelEfficiencyInput.value) || 1; // 0除算を避ける
                const gasPrice = parseFloat(gasPriceInput.value) || 0;
                
                // 計算処理
                const fuelAmount = distance / fuelEfficiency;
                const totalCost = fuelAmount * gasPrice;
                const costPerKm = totalCost / distance;
                
                // 結果の表示
                resultElement.style.display = 'block';
                resultElement.innerHTML = `
                    <div class="mb-2">
                        <h5>計算結果:</h5>
                        <p>必要な燃料: <strong>${fuelAmount.toFixed(2)} リットル</strong></p>
                        <p>総ガソリン代: <strong>${totalCost.toFixed(0)} 円</strong></p>
                        <p>1km走行あたりのコスト: <strong>${costPerKm.toFixed(2)} 円/km</strong></p>
                    </div>
                    <div class="cost-breakdown">
                        <h6>コスト内訳:</h6>
                        <div class="progress mb-2">
                            <div class="progress-bar" role="progressbar" style="width: 100%" 
                                 aria-valuenow="100" aria-valuemin="0" aria-valuemax="100">
                                ガソリン代: ${totalCost.toFixed(0)}円
                            </div>
                        </div>
                    </div>
                `;
            });
        }
    });
}

/**
 * グラフ表示機能のセットアップ
 */
function setupCharts() {
    // Chart.jsが読み込まれているか確認
    if (typeof Chart === 'undefined') {
        // Chart.jsの動的読み込み
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
        script.onload = initializeCharts;
        document.head.appendChild(script);
    } else {
        initializeCharts();
    }
}

/**
 * チャートの初期化
 */
function initializeCharts() {
    // 価格推移グラフ
    const priceHistoryChart = document.getElementById('priceHistoryChart');
    if (priceHistoryChart) {
        new Chart(priceHistoryChart, {
            type: 'line',
            data: {
                labels: ['2024年2月', '2024年5月', '2024年8月', '2024年11月', '2025年2月'],
                datasets: [{
                    label: 'レギュラーガソリン (円/L)',
                    data: [169, 173, 178, 175, 172],
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    tension: 0.2,
                    fill: true
                }, {
                    label: 'ハイオクガソリン (円/L)',
                    data: [180, 184, 189, 186, 183],
                    borderColor: '#fd7e14',
                    backgroundColor: 'rgba(253, 126, 20, 0.1)',
                    tension: 0.2,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '過去1年間のガソリン価格推移'
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        min: 160,
                        title: {
                            display: true,
                            text: '円/リットル'
                        }
                    }
                }
            }
        });
    }

    // 地域別価格比較グラフ
    const regionComparisonChart = document.getElementById('regionComparisonChart');
    if (regionComparisonChart) {
        new Chart(regionComparisonChart, {
            type: 'bar',
            data: {
                labels: ['北海道', '東北', '関東', '中部', '関西', '中国', '四国', '九州', '沖縄'],
                datasets: [{
                    label: 'レギュラーガソリン平均価格 (円/L)',
                    data: [175, 173, 172, 170, 171, 173, 174, 175, 178],
                    backgroundColor: 'rgba(13, 110, 253, 0.7)',
                    borderColor: '#0d6efd',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '2025年2月 地域別ガソリン価格'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        min: 165,
                        title: {
                            display: true,
                            text: '円/リットル'
                        }
                    }
                }
            }
        });
    }
}

/**
 * 地域セレクタ機能のセットアップ
 */
function setupRegionSelector() {
    const regionSelector = document.getElementById('regionSelector');
    const regionPriceDisplay = document.getElementById('regionPriceDisplay');
    
    if (regionSelector && regionPriceDisplay) {
        // 地域別価格データ（実際のアプリケーションではAPIから取得）
        const regionPrices = {
            'hokkaido': { regular: 175, high: 186, diesel: 152 },
            'tohoku': { regular: 173, high: 184, diesel: 150 },
            'kanto': { regular: 172, high: 183, diesel: 149 },
            'chubu': { regular: 170, high: 181, diesel: 147 },
            'kansai': { regular: 171, high: 182, diesel: 148 },
            'chugoku': { regular: 173, high: 184, diesel: 150 },
            'shikoku': { regular: 174, high: 185, diesel: 151 },
            'kyushu': { regular: 175, high: 186, diesel: 152 },
            'okinawa': { regular: 178, high: 189, diesel: 155 }
        };
        
        regionSelector.addEventListener('change', function() {
            const selectedRegion = this.value;
            const priceData = regionPrices[selectedRegion];
            
            if (priceData) {
                regionPriceDisplay.innerHTML = `
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>燃料種類</th>
                                <th>価格 (円/L)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>レギュラー</td>
                                <td><span class="price-display">${priceData.regular}</span></td>
                            </tr>
                            <tr>
                                <td>ハイオク</td>
                                <td><span class="price-display">${priceData.high}</span></td>
                            </tr>
                            <tr>
                                <td>軽油</td>
                                <td><span class="price-display">${priceData.diesel}</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <p class="data-source">出典: 資源エネルギー庁 給油所小売価格調査（2025年2月）</p>
                `;
            }
        });
        
        // 初期表示
        regionSelector.dispatchEvent(new Event('change'));
    }
}

/**
 * 要素の固定表示
 */
function setupStickyElement(selector) {
    const element = document.querySelector(selector);
    if (!element) return;
    
    const originalTop = element.getBoundingClientRect().top + window.pageYOffset;
    const footer = document.querySelector('footer');
    
    function updateStickyPosition() {
        const footerTop = footer.getBoundingClientRect().top;
        const windowHeight = window.innerHeight;
        const elementHeight = element.offsetHeight;
        
        if (window.pageYOffset > originalTop - 90) {
            element.classList.add('position-fixed');
            element.style.top = '90px';
            element.style.width = element.parentElement.offsetWidth + 'px';
            
            // フッターが見えたら固定を解除
            if (footerTop < windowHeight) {
                const diff = windowHeight - footerTop;
                element.style.top = (90 - diff) + 'px';
            }
        } else {
            element.classList.remove('position-fixed');
            element.style.top = '';
            element.style.width = '';
        }
    }
    
    if (window.innerWidth >= 992) {
        window.addEventListener('scroll', updateStickyPosition);
        window.addEventListener('resize', () => {
            if (window.innerWidth >= 992) {
                updateStickyPosition();
                element.style.width = element.parentElement.offsetWidth + 'px';
            } else {
                element.classList.remove('position-fixed');
                element.style.top = '';
                element.style.width = '';
            }
        });
    }
} 