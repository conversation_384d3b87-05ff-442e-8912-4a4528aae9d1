<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>燃費計算 | 交通費計算ツール</title>
    <meta name="description" content="実際の走行距離と給油量から正確な燃費を計算。履歴管理とグラフ表示で燃費の推移を確認できます。">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8016227945839388"
     crossorigin="anonymous"></script>
</head>
<body>
    <!-- ナビゲーションバー -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">計算ツール</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="gas.html">ガソリン代計算</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="traffic.html">交通費計算</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="fuel.html">燃費計算</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="blog.html">ブログ</a>
                    </li>
                </ul>
                <div class="dark-mode-toggle">
                    <button class="btn" id="darkModeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- メインコンテンツ -->
    <main class="container mt-5 pt-5">
        <!-- パンくずリスト -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">ホーム</a></li>
                <li class="breadcrumb-item active" aria-current="page">燃費計算</li>
            </ol>
        </nav>

        <h1 class="text-center mb-4">燃費計算</h1>
        <p class="lead text-center mb-4">走行距離と給油量から正確な燃費を計算</p>

        <!-- 計算フォーム -->
        <div class="calculator-form card shadow-sm">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <!-- 給油情報 -->
                        <div class="mb-3">
                            <label class="form-label">前回の給油量 (L)</label>
                            <input type="number" class="form-control" id="previousFuel" placeholder="例：30">
                            <small class="text-muted">前回満タンにした時の給油量</small>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">走行距離 (km)</label>
                            <input type="number" class="form-control" id="distance" placeholder="例：400">
                            <small class="text-muted">前回給油から今回給油までの走行距離</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <!-- 走行情報 -->
                        <div class="mb-3">
                            <label class="form-label">今回の給油量 (L)</label>
                            <input type="number" class="form-control" id="currentFuel" placeholder="例：35">
                            <small class="text-muted">今回満タンにした時の給油量</small>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">主な走行条件</label>
                            <div class="btn-group w-100" role="group">
                                <input type="radio" class="btn-check" name="drivingCondition" id="city" value="city">
                                <label class="btn btn-outline-primary" for="city">市街地</label>
                                <input type="radio" class="btn-check" name="drivingCondition" id="highway" value="highway" checked>
                                <label class="btn btn-outline-primary" for="highway">高速道路</label>
                                <input type="radio" class="btn-check" name="drivingCondition" id="mixed" value="mixed">
                                <label class="btn btn-outline-primary" for="mixed">混合</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="form-check mb-2">
                            <input type="checkbox" class="form-check-input" id="airConUsed">
                            <label class="form-check-label" for="airConUsed">エアコン使用</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check mb-2">
                            <input type="checkbox" class="form-check-input" id="heavyLoad">
                            <label class="form-check-label" for="heavyLoad">重い荷物を積載</label>
                        </div>
                    </div>
                </div>
                <button class="btn btn-primary w-100 mt-3" onclick="calculateFuelEfficiency()">
                    <i class="fas fa-calculator me-2"></i>燃費を計算
                </button>
            </div>
        </div>

        <!-- 計算結果 -->
        <div id="result" class="mt-4 d-none">
            <!-- JavaScript で結果を表示 -->
        </div>

        <!-- 燃費推移グラフ -->
        <section class="mt-5">
            <h2 class="mb-4">燃費推移</h2>
            <div class="card shadow-sm">
                <div class="card-body">
                    <canvas id="fuelEfficiencyChart"></canvas>
                </div>
            </div>
        </section>

        <!-- 計算方法の説明 -->
        <section class="mt-5">
            <h2 class="mb-4">燃費計算について</h2>
            <div class="row">
                <div class="col-md-4">
                    <div class="info-card card h-100">
                        <div class="card-body">
                            <h3 class="h5">基本的な計算方法</h3>
                            <ul class="list-unstyled small">
                                <li class="mb-2">◆ 燃費の計算式：</li>
                                <li>走行距離(km) ÷ 使用燃料(L)</li>
                                <li class="mt-2">◆ 使用燃料の計算：</li>
                                <li>今回の給油量 - 前回の給油量</li>
                                <li class="mt-2">◆ 正確な測定のコツ：</li>
                                <li>・必ず満タン給油する</li>
                                <li>・同じガソリンスタンドを使用</li>
                                <li>・給油時の地形を考慮</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="info-card card h-100">
                        <div class="card-body">
                            <h3 class="h5">影響する要因</h3>
                            <ul class="list-unstyled small">
                                <li class="mb-2">◆ 走行条件による変動：</li>
                                <li>・市街地：頻繁な停止と発進</li>
                                <li>・高速道路：安定した速度</li>
                                <li>・混合：両方の特徴</li>
                                <li class="mt-2">◆ その他の要因：</li>
                                <li>・エアコン使用：約10-20%増加</li>
                                <li>・重い荷物：約5-15%増加</li>
                                <li>・タイヤ空気圧：約3-5%影響</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="info-card card h-100">
                        <div class="card-body">
                            <h3 class="h5">一般的な燃費の目安</h3>
                            <ul class="list-unstyled small">
                                <li class="mb-2">◆ 車種別平均燃費：</li>
                                <li>・軽自動車：20-25km/L</li>
                                <li>・コンパクトカー：15-20km/L</li>
                                <li>・ミニバン：10-15km/L</li>
                                <li class="mt-2">◆ ハイブリッド車：</li>
                                <li>・コンパクト：30-35km/L</li>
                                <li>・セダン：25-30km/L</li>
                                <li>・SUV：20-25km/L</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="alert alert-info mt-4">
                <h4 class="h5">💡 正確な測定のために</h4>
                <ul class="mb-0">
                    <li>毎回満タン給油を心がけましょう</li>
                    <li>できるだけ同じガソリンスタンドで給油することをお勧めします</li>
                    <li>給油時の車の傾きによって誤差が生じる可能性があります</li>
                    <li>長期的な記録をつけることで、より正確な傾向が分かります</li>
                </ul>
            </div>
        </section>
    </main>

    <!-- フッター -->
    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <div class="mb-3">
                <a href="terms.html" class="text-muted mx-2">利用規約</a>
                <a href="contact.html" class="text-muted mx-2">お問い合わせ</a>
                <a href="privacy.html" class="text-muted mx-2">プライバシーポリシー</a>
            </div>
            <p class="text-muted">©2025 Masa</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-kit-code.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/calculator.js"></script>
</body>
</html>