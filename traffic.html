<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交通費計算 | 電車・バス・車の交通費簡単シミュレーション</title>
    <meta name="description" content="電車、バス、高速道路、車など、様々な交通手段の料金を簡単計算。定期券との比較や1キロあたりの単価計算、通勤費・ガソリン代計算も可能。">
    <meta name="keywords" content="交通費計算,交通費計算 電車,交通費計算 表,交通費計算 車,交通費計算 シミュレーション,交通費計算 簡単,交通費計算 1キロいくら,交通費計算 定期">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <style>
        /* 計算ツールとグラフのスタイル */
        .calculator-container {
            min-height: 650px;
            position: relative;
        }
        
        #draggableCalculator {
            cursor: move;
            position: relative;
            z-index: 10;
            transition: box-shadow 0.3s;
        }
        
        #draggableCalculator:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        .drag-handle {
            cursor: grab;
            background-color: rgba(255, 255, 255, 0.2);
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            transition: all 0.2s;
        }
        
        .drag-handle:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
        
        .drag-handle:active {
            cursor: grabbing;
            background-color: rgba(255, 255, 255, 0.4);
        }
        
        /* 禁止计算器内部元素的拖动行为 */
        #draggableCalculator input,
        #draggableCalculator select,
        #draggableCalculator button,
        #draggableCalculator .form-check-input,
        #draggableCalculator .form-range {
            touch-action: auto !important;
        }
        
        .chart-container {
            height: 100%;
        }
        
        .result-box {
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
        }
        
        /* スライダーのスタイル */
        .form-range::-webkit-slider-thumb {
            background-color: #0d6efd;
        }
        
        .form-range::-moz-range-thumb {
            background-color: #0d6efd;
        }
        
        .form-range::-ms-thumb {
            background-color: #0d6efd;
        }
        
        /* タブコンテンツのスタイル */
        .tab-content {
            min-height: 300px;
        }
        
        /* グラフキャンバスのスタイル */
        canvas {
            max-width: 100%;
        }
        
        /* レスポンシブ対応 */
        @media (max-width: 767.98px) {
            .calculator-container {
                min-height: auto;
            }
            
            #draggableCalculator {
                margin-bottom: 2rem;
                position: static !important;
                transform: none !important;
            }
            
            .chart-container {
                height: auto;
            }
        }
    </style>
<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8016227945839388"
     crossorigin="anonymous"></script>
</head>
<body>
    <!-- ナビゲーションバー -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">計算ツール</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="gas.html">ガソリン代計算</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="traffic.html">交通費計算</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fuel.html">燃費計算</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="blog.html">ブログ</a>
                    </li>
                </ul>
                <div class="dark-mode-toggle">
                    <button class="btn" id="darkModeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- メインコンテンツ -->
    <main class="container mt-5 pt-5">
        <!-- パンくずリスト -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">ホーム</a></li>
                <li class="breadcrumb-item active" aria-current="page">交通費計算</li>
            </ol>
        </nav>

        <h1 class="text-center mb-4">交通費計算シミュレーション</h1>
        <p class="lead text-center mb-4">電車・バス・高速道路・車の交通費を簡単計算できるツール</p>

        <!-- 計算ツールとグラフ表示エリア -->
        <div class="calculator-container row mt-4 mb-5">
            <!-- 左側：ドラッグ可能な計算ツール -->
            <div class="col-md-6">
                <div class="calculator-form card shadow-sm" id="draggableCalculator">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">交通費計算ツール</h5>
                        <div class="drag-handle">
                            <i class="fas fa-grip-lines"></i> ドラッグ
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <!-- 交通手段選択 -->
                                <div class="mb-3">
                                    <label class="form-label">交通手段</label>
                                    <div class="btn-group w-100" role="group">
                                        <input type="radio" class="btn-check" name="transportType" id="train" value="train" checked>
                                        <label class="btn btn-outline-primary" for="train">電車</label>
                                        <input type="radio" class="btn-check" name="transportType" id="bus" value="bus">
                                        <label class="btn btn-outline-primary" for="bus">バス</label>
                                        <input type="radio" class="btn-check" name="transportType" id="highway" value="highway">
                                        <label class="btn btn-outline-primary" for="highway">高速道路</label>
                                        <input type="radio" class="btn-check" name="transportType" id="car" value="car">
                                        <label class="btn btn-outline-primary" for="car">車（ガソリン）</label>
                                    </div>
                                </div>

                                <!-- 距離入力 -->
                                <div class="mb-3">
                                    <label class="form-label">距離 (km)</label>
                                    <input type="range" class="form-range" id="distanceRange" min="1" max="1000" step="1" value="100">
                                    <div class="d-flex justify-content-between">
                                        <input type="number" class="form-control w-75" id="distance" placeholder="例：100" value="100">
                                        <span class="ms-2 pt-2">km</span>
                                    </div>
                                </div>
                                
                                <!-- 往復チェック -->
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="roundTrip">
                                    <label class="form-check-label" for="roundTrip">往復で計算</label>
                                </div>

                                <!-- 通勤・通学選択 -->
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="commute">
                                    <label class="form-check-label" for="commute">通勤・通学費として計算</label>
                                    <small class="d-block text-muted">通勤費として計算すると、非課税部分の表示や課税対象の計算ができます</small>
                                </div>

                                <!-- 交通手段別オプション -->
                                <div class="options-container">
                                    <!-- オプション設定 -->
                                    <label class="form-label">オプション</label>
                                    
                                    <!-- 電車オプション -->
                                    <div id="trainOptions">
                                        <div class="mb-3">
                                            <label class="form-label">季節設定</label>
                                            <select class="form-select" id="seasonSelect">
                                                <option value="normal">通常期</option>
                                                <option value="peak">ピーク期（お盆・年末年始）</option>
                                                <option value="offPeak">オフピーク期</option>
                                            </select>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="expressOption">
                                            <label class="form-check-label" for="expressOption">特急利用</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="reservedSeatOption">
                                            <label class="form-check-label" for="reservedSeatOption">指定席</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="greenCarOption">
                                            <label class="form-check-label" for="greenCarOption">グリーン車</label>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">定期券比較</label>
                                            <select class="form-select" id="commuterPass">
                                                <option value="none">利用しない</option>
                                                <option value="oneMonth">1ヶ月定期</option>
                                                <option value="threeMonth">3ヶ月定期</option>
                                                <option value="sixMonth">6ヶ月定期</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <!-- 高速道路オプション -->
                                    <div id="highwayOptions" style="display: none;">
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="etcOption">
                                            <label class="form-check-label" for="etcOption">ETC割引適用</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="holidayOption">
                                            <label class="form-check-label" for="holidayOption">休日割引適用</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="nightTimeOption">
                                            <label class="form-check-label" for="nightTimeOption">深夜割引（22時-6時）</label>
                                        </div>
                                    </div>
                                    
                                    <!-- 車（ガソリン）オプション -->
                                    <div id="carOptions" style="display: none;">
                                        <div class="mb-3">
                                            <label class="form-label">燃費 (km/L)</label>
                                            <input type="number" class="form-control" id="fuelEfficiency" placeholder="例：15" value="15">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">ガソリン単価 (円/L)</label>
                                            <input type="number" class="form-control" id="gasPrice" placeholder="例：150" value="165">
                                            <small class="text-muted">現在の平均価格: 165円/L</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button class="btn btn-primary w-100 mt-3" onclick="calculateTrafficCost()">
                            <i class="fas fa-calculator me-2"></i>交通費を計算
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 右側：リアルタイムグラフ表示 -->
            <div class="col-md-6">
                <div class="chart-container card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">交通費比較チャート</h5>
                    </div>
                    <div class="card-body">
                        <!-- 計算結果サマリー -->
                        <div id="result" class="mb-4">
                            <div class="result-summary">
                                <h3 class="h5">計算結果サマリー</h3>
                                <div class="result-box p-3 bg-light rounded mb-3">
                                    <div class="row">
                                        <div class="col-6">
                                            <p class="mb-1">片道料金:</p>
                                            <h4 id="oneWayCost">¥0</h4>
                                        </div>
                                        <div class="col-6">
                                            <p class="mb-1">往復料金:</p>
                                            <h4 id="roundTripCost">¥0</h4>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-6">
                                            <p class="mb-1">月額費用 (22日):</p>
                                            <h4 id="monthlyCost">¥0</h4>
                                        </div>
                                        <div class="col-6">
                                            <p class="mb-1">1km当たり:</p>
                                            <h4 id="costPerKm">¥0</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- グラフ表示エリア -->
                        <div class="chart-area">
                            <ul class="nav nav-tabs" id="chartTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="comparison-tab" data-bs-toggle="tab" data-bs-target="#comparison" type="button" role="tab">交通手段比較</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="monthly-tab" data-bs-toggle="tab" data-bs-target="#monthly" type="button" role="tab">月額費用</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="distance-tab" data-bs-toggle="tab" data-bs-target="#distance" type="button" role="tab">距離別料金</button>
                                </li>
                            </ul>
                            <div class="tab-content p-3 border border-top-0 rounded-bottom" id="chartTabContent">
                                <div class="tab-pane fade show active" id="comparison" role="tabpanel">
                                    <canvas id="transportComparisonChart" height="250"></canvas>
                                </div>
                                <div class="tab-pane fade" id="monthly" role="tabpanel">
                                    <canvas id="monthlyComparisonChart" height="250"></canvas>
                                </div>
                                <div class="tab-pane fade" id="distance" role="tabpanel">
                                    <canvas id="distanceComparisonChart" height="250"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 交通費とは？税金関連情報 -->
        <section class="mt-5">
            <h2 class="mb-4">交通費とは？税金関連情報</h2>
            <div class="card mb-4">
                <div class="card-body">
                    <h3 class="h5 mb-3">交通費の基本知識</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <h4 class="h6">交通費の定義</h4>
                            <p>交通費とは、通勤や業務のために利用する交通機関の料金や燃料費のことを指します。会社員の場合は通勤手当として支給されることが多く、自営業者の場合は経費として計上できる場合があります。</p>
                        </div>
                        <div class="col-md-6">
                            <h4 class="h6">交通費 非課税と課税対象</h4>
                            <p>会社から支給される通勤手当（交通費）は、一定の金額まで非課税となります。現在の非課税限度額は、通勤手段により異なります：</p>
                            <ul>
                                <li>電車・バスなど：月額15万円まで</li>
                                <li>車・バイク：距離に応じた金額（2〜31,600円/月）</li>
                            </ul>
                        </div>
                    </div>
                    <div class="alert alert-info mt-3">
                        <strong>ポイント：</strong> 通勤費（交通費）は、最も経済的かつ合理的な経路で計算されるのが一般的です。このツールでは、交通費計算と合わせて非課税限度額も確認できます。
                    </div>
                </div>
            </div>
        </section>

        <!-- 計算方法の説明 -->
        <section class="mt-5">
            <h2 class="mb-4">交通費計算方法について</h2>
            <div class="row">
                <div class="col-md-4">
                    <div class="info-card card h-100">
                        <div class="card-body">
                            <h3 class="h5">電車運賃の計算方法</h3>
                            <ul class="list-unstyled small">
                                <li class="mb-2">◆ 基本運賃（JR規定）：</li>
                                <li>・3kmまで：150円</li>
                                <li>・6kmまで：190円</li>
                                <li>・10kmまで：200円</li>
                                <li>・15kmまで：260円</li>
                                <li>・以降は距離に応じて加算</li>
                                <li class="mt-2">◆ 特急料金：</li>
                                <li>・基本料金：950円</li>
                                <li>・距離加算：15円/km</li>
                                <li class="mt-2">◆ 指定席料金：</li>
                                <li>・通常：520円</li>
                                <li>・長距離（601km以上）：1,040円</li>
                            </ul>
                            <div class="mt-3">
                                <span class="badge bg-info">1キロいくら？</span>
                                <p class="small mt-1">電車の場合、距離帯によって異なりますが、平均で約20〜25円/kmです。</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="info-card card h-100">
                        <div class="card-body">
                            <h3 class="h5">各種割引について</h3>
                            <ul class="list-unstyled small">
                                <li class="mb-2">◆ 季節料金変動：</li>
                                <li>・通常期：基本料金</li>
                                <li>・ピーク期：20%増</li>
                                <li>・オフピーク期：10%引き</li>
                                <li class="mt-2">◆ ETC割引（高速道路）：</li>
                                <li>・基本割引：10%</li>
                                <li>・休日割引：30%</li>
                                <li>・深夜割引：50%</li>
                                <li class="mt-2">◆ 距離別割引（高速道路）：</li>
                                <li>・100km以上：5%引き</li>
                                <li>・200km以上：10%引き</li>
                                <li>・500km以上：15%引き</li>
                            </ul>
                            <div class="mt-3">
                                <span class="badge bg-info">車の交通費</span>
                                <p class="small mt-1">ガソリン代だけでなく、高速道路料金や駐車場代も交通費として考慮します。</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="info-card card h-100">
                        <div class="card-body">
                            <h3 class="h5">定期券・その他</h3>
                            <ul class="list-unstyled small">
                                <li class="mb-2">◆ 定期券の計算：</li>
                                <li>・1ヶ月：通常運賃の30日分</li>
                                <li>・3ヶ月：1ヶ月定期の2.8倍</li>
                                <li>・6ヶ月：1ヶ月定期の5.3倍</li>
                                <li class="mt-2">◆ バス運賃：</li>
                                <li>・基本運賃：20円/km</li>
                                <li>・最低運賃：220円</li>
                                <li>・深夜割増：30%増</li>
                                <li class="mt-2">◆ グリーン車料金：</li>
                                <li>・通常：2,200円</li>
                                <li>・長距離：3,300円</li>
                            </ul>
                            <div class="mt-3">
                                <span class="badge bg-info">定期券込みの交通費</span>
                                <p class="small mt-1">通勤・通学では、定期券込みの交通費で計算するとお得になるケースが多いです。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="alert alert-info mt-4">
                <h4 class="h5">💡 交通費計算のヒント</h4>
                <ul class="mb-0">
                    <li>往復の場合は自動的に2倍で計算されます</li>
                    <li>複数の割引が適用される場合は、順番に計算されます</li>
                    <li>定期券との比較は、平均営業日（22日/月）で計算されます</li>
                    <li>距離は片道分を入力してください</li>
                    <li>通勤費として計算する場合、非課税限度額も確認できます</li>
                </ul>
            </div>
        </section>

        <!-- 交通費計算表について -->
        <section class="mt-5">
            <h2 class="mb-4">交通費計算表の活用方法</h2>
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h3 class="h5">交通費計算表のメリット</h3>
                            <p>交通費計算表を使うことで、以下のような利点があります：</p>
                            <ul>
                                <li>毎月の交通費予算を正確に把握できる</li>
                                <li>経路ごとの料金を一覧できるため比較検討が容易</li>
                                <li>定期代と切符代の費用対効果を分析できる</li>
                                <li>通勤・通学の最適ルートを見つけるのに役立つ</li>
                                <li>出張や旅行の費用計画に活用できる</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h3 class="h5">交通費計算サイト活用術</h3>
                            <p>このような交通費計算サイトを最大限活用するには：</p>
                            <ul>
                                <li>複数の交通手段を比較してコスト効率の良い方法を見つける</li>
                                <li>定期利用と都度払いの損益分岐点を計算する</li>
                                <li>通勤・通学の頻度に応じた最適な定期券期間を選ぶ</li>
                                <li>出張・旅行時の交通費をシミュレーションし予算を立てる</li>
                                <li>経費精算や確定申告の際の参考資料として活用する</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- お役立ち情報 -->
        <section class="mt-5">
            <h2 class="mb-4">交通費を節約するコツ</h2>
            <div class="row">
                <div class="col-md-4">
                    <div class="info-card card h-100">
                        <div class="card-body">
                            <h3 class="h5">定期券の活用</h3>
                            <p>定期券でお得に移動：</p>
                            <ul class="list-unstyled">
                                <li>✓ 通勤・通学定期の比較</li>
                                <li>✓ 回数券との費用比較</li>
                                <li>✓ 定期区間の有効活用</li>
                                <li>✓ 連絡定期の検討</li>
                                <li>✓ 定期込みの交通費で節約</li>
                            </ul>
                            <div class="mt-3">
                                <p class="small">定期区間内であれば、途中下車しても追加料金がかかりません。定期券の特性を活かした移動計画を立てましょう。</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="info-card card h-100">
                        <div class="card-body">
                            <h3 class="h5">割引きっぷ</h3>
                            <p>お得なきっぷ情報：</p>
                            <ul class="list-unstyled">
                                <li>✓ 時間帯による割引</li>
                                <li>✓ 早割・特割きっぷ</li>
                                <li>✓ 周遊きっぷ</li>
                                <li>✓ 企画乗車券</li>
                                <li>✓ シーズン限定割引</li>
                            </ul>
                            <div class="mt-3">
                                <p class="small">事前予約や特定時間帯の利用で大幅な割引が受けられることがあります。特に長距離移動の場合は、割引きっぷの活用で交通費を大幅に節約できます。</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="info-card card h-100">
                        <div class="card-body">
                            <h3 class="h5">交通系ICカード</h3>
                            <p>ICカードの活用方法：</p>
                            <ul class="list-unstyled">
                                <li>✓ ポイント還元の活用</li>
                                <li>✓ オートチャージの設定</li>
                                <li>✓ 相互利用エリアの確認</li>
                                <li>✓ 電子マネーとしての利用</li>
                                <li>✓ クレジットカード連携でポイント二重取り</li>
                            </ul>
                            <div class="mt-3">
                                <p class="small">ICカードのポイントプログラムを活用すれば、実質的な交通費削減につながります。また、定期券機能付きICカードなら、定期区間外も自動的に精算されて便利です。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- アプリ・ツール紹介 -->
        <section class="mt-5">
            <h2 class="mb-4">便利な交通費計算アプリ・ツール</h2>
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h3 class="h5">スマホで使える交通費計算アプリ</h3>
                            <p>外出先でも簡単に交通費をチェックできるアプリをご紹介します：</p>
                            <ul>
                                <li><strong>乗換案内アプリ</strong> - 経路検索と運賃計算を同時に行えます</li>
                                <li><strong>交通費精算アプリ</strong> - 経費精算に便利な記録・計算機能付き</li>
                                <li><strong>燃費計算アプリ</strong> - 車での移動費用を正確に把握できます</li>
                                <li><strong>高速道路料金検索</strong> - ETCや各種割引を含めた正確な料金計算</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h3 class="h5">シミュレーションと比較</h3>
                            <p>交通費計算シミュレーションを活用して、効率的な移動計画を：</p>
                            <ul>
                                <li>複数の交通手段を比較し、最もコスト効率の良い方法を選択</li>
                                <li>定期券と回数券のコスト比較で最適な購入判断</li>
                                <li>出張・旅行前の予算シミュレーションに活用</li>
                                <li>通勤手当の申請や交通費精算の際の根拠資料として使用</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- フッター -->
    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <div class="mb-3">
                <a href="terms.html" class="text-muted mx-2">利用規約</a>
                <a href="contact.html" class="text-muted mx-2">お問い合わせ</a>
                <a href="privacy.html" class="text-muted mx-2">プライバシーポリシー</a>
            </div>
            <p class="text-muted">©2025 Masa</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/js/all.min.js"></script>
    <!-- Chart.js for charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <!-- interact.js for draggable functionality -->
    <script src="https://cdn.jsdelivr.net/npm/interactjs@1.10.17/dist/interact.min.js"></script>
    <script src="js/calculator.js"></script>
    
    <script>
        // Make the calculator draggable
        interact('#draggableCalculator')
            .draggable({
                // Enable inertial throwing
                inertia: true,
                // Keep the element within the area of its parent
                modifiers: [
                    interact.modifiers.restrictRect({
                        restriction: 'parent',
                        endOnly: true
                    })
                ],
                // Enable autoScroll
                autoScroll: true,
                
                // 防止在输入框和控件操作时触发拖动
                ignoreFrom: 'input, button, select, .form-check-input, .form-range, .form-select, .form-check-label',
                
                // 只允许通过拖动手柄来移动
                allowFrom: '.drag-handle, .card-header',
                
                listeners: {
                    // Call this function on every dragmove event
                    move: dragMoveListener,
                }
            });

        function dragMoveListener(event) {
            const target = event.target;
            // Keep the dragged position in the data-x/data-y attributes
            const x = (parseFloat(target.getAttribute('data-x')) || 0) + event.dx;
            const y = (parseFloat(target.getAttribute('data-y')) || 0) + event.dy;

            // Translate the element
            target.style.transform = `translate(${x}px, ${y}px)`;

            // Update the position attributes
            target.setAttribute('data-x', x);
            target.setAttribute('data-y', y);
        }

        // Chart initialization
        let transportComparisonChart, monthlyComparisonChart, distanceComparisonChart;

        // Initialize charts
        function initCharts() {
            // Transport comparison chart
            const transportCtx = document.getElementById('transportComparisonChart').getContext('2d');
            transportComparisonChart = new Chart(transportCtx, {
                type: 'bar',
                data: {
                    labels: ['電車', 'バス', '高速道路', '車（ガソリン）'],
                    datasets: [{
                        label: '片道料金 (円)',
                        data: [0, 0, 0, 0],
                        backgroundColor: [
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(255, 159, 64, 0.7)',
                            'rgba(153, 102, 255, 0.7)'
                        ],
                        borderColor: [
                            'rgba(54, 162, 235, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(255, 159, 64, 1)',
                            'rgba(153, 102, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: '交通手段別料金比較'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Monthly comparison chart
            const monthlyCtx = document.getElementById('monthlyComparisonChart').getContext('2d');
            monthlyComparisonChart = new Chart(monthlyCtx, {
                type: 'bar',
                data: {
                    labels: ['通常運賃 (22日)', '1ヶ月定期', '3ヶ月定期 (月換算)', '6ヶ月定期 (月換算)'],
                    datasets: [{
                        label: '月額料金 (円)',
                        data: [0, 0, 0, 0],
                        backgroundColor: [
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(255, 159, 64, 0.7)',
                            'rgba(153, 102, 255, 0.7)'
                        ],
                        borderColor: [
                            'rgba(54, 162, 235, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(255, 159, 64, 1)',
                            'rgba(153, 102, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: '通常運賃 vs 定期券'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Distance comparison chart
            const distanceCtx = document.getElementById('distanceComparisonChart').getContext('2d');
            distanceComparisonChart = new Chart(distanceCtx, {
                type: 'line',
                data: {
                    labels: ['10km', '20km', '30km', '50km', '100km'],
                    datasets: [{
                        label: '電車',
                        data: [0, 0, 0, 0, 0],
                        borderColor: 'rgba(54, 162, 235, 1)',
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        tension: 0.1,
                        fill: true
                    }, {
                        label: 'バス',
                        data: [0, 0, 0, 0, 0],
                        borderColor: 'rgba(75, 192, 192, 1)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1,
                        fill: true
                    }, {
                        label: '車（ガソリン）',
                        data: [0, 0, 0, 0, 0],
                        borderColor: 'rgba(153, 102, 255, 1)',
                        backgroundColor: 'rgba(153, 102, 255, 0.2)',
                        tension: 0.1,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: '距離別料金比較'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '料金 (円)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '距離 (km)'
                            }
                        }
                    }
                }
            });
        }

        // Function to update charts with calculation results
        function updateCharts(result) {
            // Sample result object structure
            // result = {
            //     train: { oneWay: 1500, roundTrip: 3000, monthly: 33000 },
            //     bus: { oneWay: 1000, roundTrip: 2000, monthly: 22000 },
            //     highway: { oneWay: 2000, roundTrip: 4000, monthly: 44000 },
            //     car: { oneWay: 1200, roundTrip: 2400, monthly: 26400 }
            // };

            // Update transport comparison chart
            transportComparisonChart.data.datasets[0].data = [
                result.train.oneWay, 
                result.bus.oneWay, 
                result.highway.oneWay, 
                result.car.oneWay
            ];
            transportComparisonChart.update();

            // Update monthly comparison chart
            const monthlyRegular = result.train.oneWay * 2 * 22; // 22 working days per month
            const monthlyPass1 = monthlyRegular * 0.7; // 1-month pass (estimated 30% discount)
            const monthlyPass3 = monthlyPass1 * 0.95; // 3-month pass (additional 5% discount)
            const monthlyPass6 = monthlyPass1 * 0.9; // 6-month pass (additional 10% discount)

            monthlyComparisonChart.data.datasets[0].data = [
                monthlyRegular,
                monthlyPass1,
                monthlyPass3,
                monthlyPass6
            ];
            monthlyComparisonChart.update();

            // Update distance comparison chart
            const distanceValues = [10, 20, 30, 50, 100];
            const trainData = distanceValues.map(distance => calculateTrainFare(distance));
            const busData = distanceValues.map(distance => calculateBusFare(distance));
            const carData = distanceValues.map(distance => calculateCarFare(distance));

            distanceComparisonChart.data.datasets[0].data = trainData;
            distanceComparisonChart.data.datasets[1].data = busData;
            distanceComparisonChart.data.datasets[2].data = carData;
            distanceComparisonChart.update();

            // Update summary result display
            document.getElementById('oneWayCost').textContent = `¥${result.train.oneWay.toLocaleString()}`;
            document.getElementById('roundTripCost').textContent = `¥${result.train.roundTrip.toLocaleString()}`;
            document.getElementById('monthlyCost').textContent = `¥${monthlyRegular.toLocaleString()}`;
            
            const distance = parseFloat(document.getElementById('distance').value) || 0;
            const costPerKm = distance > 0 ? Math.round(result.train.oneWay / distance) : 0;
            document.getElementById('costPerKm').textContent = `¥${costPerKm.toLocaleString()}`;
        }

        // Simple calculation functions for chart data
        function calculateTrainFare(distance) {
            // Simplified calculation
            if (distance <= 3) return 150;
            if (distance <= 6) return 190;
            if (distance <= 10) return 200;
            if (distance <= 15) return 260;
            return 260 + Math.ceil((distance - 15) / 5) * 40;
        }

        function calculateBusFare(distance) {
            // Simplified calculation
            return Math.max(220, Math.ceil(distance * 20));
        }

        function calculateCarFare(distance) {
            // Simplified calculation - based on average 15km/L and 165 yen/L
            const fuelEfficiency = 15;
            const gasPrice = 165;
            return Math.ceil(distance * gasPrice / fuelEfficiency);
        }

        // Sync the range slider with the number input
        document.getElementById('distanceRange').addEventListener('input', function() {
            document.getElementById('distance').value = this.value;
        });
        
        document.getElementById('distance').addEventListener('input', function() {
            document.getElementById('distanceRange').value = this.value;
        });

        // Show/hide option sets based on transport type selection
        document.querySelectorAll('input[name="transportType"]').forEach(input => {
            input.addEventListener('change', function() {
                // Hide all option sets
                document.getElementById('trainOptions').style.display = 'none';
                document.getElementById('highwayOptions').style.display = 'none';
                document.getElementById('carOptions').style.display = 'none';
                
                // Show the selected one
                if (this.value === 'train') {
                    document.getElementById('trainOptions').style.display = 'block';
                } else if (this.value === 'highway') {
                    document.getElementById('highwayOptions').style.display = 'block';
                } else if (this.value === 'car') {
                    document.getElementById('carOptions').style.display = 'block';
                }
            });
        });

        // Mock calculation function (to be replaced with real calculation)
        function calculateTrafficCost() {
            const distance = parseFloat(document.getElementById('distance').value) || 0;
            const roundTrip = document.getElementById('roundTrip').checked;
            const selectedTransport = document.querySelector('input[name="transportType"]:checked').value;
            
            // Mock result - replace with actual calculation
            const baseTrainFare = calculateTrainFare(distance);
            const baseBusFare = calculateBusFare(distance);
            const baseHighwayFare = distance * 24.6; // approx 24.6 yen per km
            const baseCarFare = calculateCarFare(distance);
            
            const result = {
                train: {
                    oneWay: baseTrainFare,
                    roundTrip: baseTrainFare * 2,
                    monthly: baseTrainFare * 2 * 22
                },
                bus: {
                    oneWay: baseBusFare,
                    roundTrip: baseBusFare * 2,
                    monthly: baseBusFare * 2 * 22
                },
                highway: {
                    oneWay: baseHighwayFare,
                    roundTrip: baseHighwayFare * 2,
                    monthly: baseHighwayFare * 2 * 22
                },
                car: {
                    oneWay: baseCarFare,
                    roundTrip: baseCarFare * 2,
                    monthly: baseCarFare * 2 * 22
                }
            };
            
            updateCharts(result);
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            // Initial calculation with default values
            calculateTrafficCost();
            
            // 禁止表单元素冒泡拖动事件
            const formElements = document.querySelectorAll('#draggableCalculator input, #draggableCalculator select, #draggableCalculator button');
            formElements.forEach(element => {
                element.addEventListener('mousedown', function(e) {
                    e.stopPropagation();
                });
                element.addEventListener('touchstart', function(e) {
                    e.stopPropagation();
                }, { passive: true });
            });
            
            // 禁用移动设备上的拖动功能（只通过拖动手柄触发）
            if ('ontouchstart' in window) {
                document.querySelector('#draggableCalculator').addEventListener('touchstart', function(e) {
                    if (!e.target.closest('.drag-handle') && !e.target.closest('.card-header')) {
                        e.stopPropagation();
                    }
                }, { passive: true });
            }
        });
    </script>
</body>
</html>