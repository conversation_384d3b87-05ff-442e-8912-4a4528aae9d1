<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ガソリン代計算ツール | 距離・車種別に簡単計算</title>
    <meta name="description" content="無料で使えるガソリン代計算ツール。距離や車種（軽自動車など）から正確なガソリン代を計算。燃費計算、高速道路料金、月間コスト試算、履歴保存機能付き。スマホアプリ感覚で簡単操作！">
    <meta name="keywords" content="ガソリン代計算,ガソリン代計算 距離,ガソリン代計算 軽自動車,交通費計算,燃費計算,ガソリン代 平均,高速道路料金,ETC割引,通勤費用,ガソリン代計算サイト">
    <meta name="author" content="Masa">
    <meta name="google-adsense-account" content="ca-pub-****************">
    <meta property="og:title" content="ガソリン代計算ツール | 距離・車種別に簡単計算">
    <meta property="og:description" content="無料で使えるガソリン代計算ツール。距離や車種から正確なガソリン代を計算。燃費計算、高速道路料金、月間コスト試算、履歴保存機能付き。">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://gascalc.blog/">
    <meta name="twitter:card" content="summary_large_image">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "ガソリン代計算ツール",
      "description": "無料で使えるガソリン代計算ツール。距離や車種から正確なガソリン代を計算。燃費計算、高速道路料金、月間コスト試算、履歴保存機能付き。",
      "applicationCategory": "UtilityApplication",
      "operatingSystem": "All",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "JPY"
      }
    }
    </script>
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "このガソリン代計算ツールは無料で使えますか？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "はい、完全無料でご利用いただけます。会員登録も不要です。スマホやPCからアクセスするだけで、アプリのようにすぐに使えます。"
          }
        },
        {
          "@type": "Question",
          "name": "ガソリン代計算の精度はどのくらいですか？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "実際の走行条件（道路状況、運転方法、天候など）により変動する可能性がありますが、一般的な使用では誤差5%以内を目指しています。より正確な結果を得るために、実際の燃費データを入力することをお勧めします。特に軽自動車など車種によって燃費が大きく異なるため、正確な車種の選択が重要です。"
          }
        },
        {
          "@type": "Question",
          "name": "ガソリン代計算の履歴は保存されますか？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "はい、最新10件の計算履歴がブラウザに保存されます。ブラウザのデータをクリアすると履歴も削除されます。履歴機能により、定期的な移動のガソリン代を簡単に比較できます。"
          }
        },
        {
          "@type": "Question",
          "name": "高速道路料金の計算にはETC割引が含まれていますか？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "はい、ETCの基本割引（約10%）を適用するかどうかを選択できます。ただし、各種キャンペーンや深夜割引などの特別割引は含まれていません。高速道路料金とガソリン代を合わせた総交通費を計算できます。"
          }
        },
        {
          "@type": "Question",
          "name": "スマートフォンでもガソリン代計算ツールは使えますか？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "はい、スマートフォンやタブレットなど、様々な端末で快適にご利用いただけます。画面サイズに合わせて最適化されているため、専用アプリをインストールしなくても、ブラウザから簡単にアクセスできます。"
          }
        },
        {
          "@type": "Question",
          "name": "地図を使ったガソリン代計算はできますか？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "現在、直接地図連携機能はありませんが、Google マップなどで距離を調べてから、その数値を当ツールに入力することで正確なガソリン代を計算できます。将来的には地図連携機能の追加も検討しています。"
          }
        },
        {
          "@type": "Question",
          "name": "ガソリン代の割り勘計算はどうすればいいですか？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "当ツールで計算したガソリン代の合計金額を、乗車人数で割ることで簡単に割り勘金額を算出できます。例えば、往復500kmの移動でガソリン代が5,000円の場合、4人で乗車なら1人あたり1,250円となります。高速道路料金も含めた総額での割り勘も計算可能です。"
          }
        }
      ]
    }
    </script>
    <meta http-equiv="Content-Security-Policy" content="
        default-src 'self';
        script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://kit.fontawesome.com https://cdnjs.cloudflare.com;
        style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;
        img-src 'self' data: https:;
        font-src 'self' https://cdnjs.cloudflare.com;
        connect-src 'self' https://ka-f.fontawesome.com;
        frame-src 'self';
        base-uri 'self';
        form-action 'self'
    ">
<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
     crossorigin="anonymous"></script>
</head>
<body>
    <!-- ナビゲーションバー -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">ガソリン代計算ツール</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="gas.html">ガソリン代計算</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="traffic.html">交通費計算</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fuel.html">燃費計算</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="blog.html">ブログ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about-gas-calculation">計算方法</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#faq">よくある質問</a>
                    </li>
                </ul>
                <!-- ダークモードトグル -->
                <div class="dark-mode-toggle">
                    <button class="btn" id="darkModeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- メインコンテンツ -->
    <main class="container mt-5 pt-5">
        <h1 class="text-center mb-4">ガソリン代計算ツール | 距離・車種別に簡単算出</h1>
        <p class="lead text-center mb-4">距離や車種（軽自動車など）からガソリン代を正確に計算できる無料ツール。燃費計算、高速道路料金、月間コスト試算、履歴保存機能付き。スマホでもPCでも使いやすいアプリ感覚のサイトです。</p>
        
        <div class="text-center mb-4">
            <a href="blog.html" class="btn btn-outline-primary me-2">
                <i class="fas fa-book-open me-1"></i> ガソリン代計算に関する記事を読む
            </a>
        </div>

        <!-- 新しい計算エリア - フレックスボックスレイアウト -->
        <div class="calculator-container">
            <!-- タブメニュー -->
            <ul class="nav nav-tabs mb-4" id="calculatorTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#gasCalc">ガソリン代計算</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#monthlyCalc">月間試算</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#highwayCalc">高速道路料金</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#history">計算履歴</a>
                </li>
            </ul>

            <!-- タブコンテンツ -->
            <div class="tab-content">
                <!-- ガソリン代計算タブ -->
                <div class="tab-pane fade show active" id="gasCalc">
                    <div class="calculator-box">
                        <div class="calculator-input" id="gasCalcInput">
                            <div class="handle-bar"><div class="handle"></div></div>
                            <div class="card-body">
                                <form id="gasCalculator">
                                    <!-- 車種選択 -->
                                    <div class="mb-3">
                                        <label for="carType" class="form-label">車種選択</label>
                                        <select class="form-select" id="carType">
                                            <option value="custom">手動入力</option>
                                            <option value="compact">軽自動車 (平均燃費: 25km/L)</option>
                                            <option value="sedan">セダン (平均燃費: 15km/L)</option>
                                            <option value="suv">SUV (平均燃費: 12km/L)</option>
                                        </select>
                                    </div>

                                    <!-- ガソリン価格 -->
                                    <div class="mb-3">
                                        <label for="gasPrice" class="form-label">ガソリン価格 (円/L)</label>
                                        <input type="number" class="form-control" id="gasPrice" placeholder="150" value="150">
                                    </div>

                                    <!-- 走行距離 -->
                                    <div class="mb-3">
                                        <label for="distance" class="form-label">走行距離 (km)</label>
                                        <input type="number" class="form-control" id="distance" placeholder="100" value="100">
                                    </div>

                                    <!-- 燃費 -->
                                    <div class="mb-3">
                                        <label for="fuelEfficiency" class="form-label">燃費 (km/L)</label>
                                        <input type="number" class="form-control" id="fuelEfficiency" placeholder="15" value="15">
                                    </div>

                                    <!-- 往復チェックボックス -->
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="roundTrip">
                                        <label class="form-check-label" for="roundTrip">往復で計算</label>
                                    </div>

                                    <button type="button" class="btn btn-primary w-100" id="calculateButton">計算する</button>
                                </form>
                            </div>
                        </div>
                        <div class="calculator-result">
                            <div id="resultDisplay">
                                <h4>ガソリン代計算結果</h4>
                                <div id="resultData">
                                    <p class="text-muted">入力項目を設定して計算ボタンをクリックしてください</p>
                                </div>
                                <div class="chart-container">
                                    <canvas id="gasChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 月間ガソリン代計算タブ -->
                <div class="tab-pane fade" id="monthlyCalc">
                    <div class="calculator-box">
                        <div class="calculator-input" id="monthlyCalcInput">
                            <div class="handle-bar"><div class="handle"></div></div>
                            <div class="card-body">
                                <!-- 車種選択 -->
                                <div class="mb-3">
                                    <label class="form-label">車種選択</label>
                                    <select class="form-select" id="monthlyCarType">
                                        <option value="custom">手動入力</option>
                                        <option value="compact">軽自動車 (平均燃費: 25km/L)</option>
                                        <option value="sedan">セダン (平均燃費: 15km/L)</option>
                                        <option value="suv">SUV (平均燃費: 12km/L)</option>
                                    </select>
                                </div>

                                <!-- 燃費入力 -->
                                <div class="mb-3">
                                    <label class="form-label">燃費 (km/L)</label>
                                    <input type="number" class="form-control" id="monthlyFuelEfficiency" placeholder="15" value="15">
                                </div>

                                <!-- 燃料タイプ -->
                                <div class="mb-3">
                                    <label class="form-label">燃料タイプ</label>
                                    <div class="btn-group w-100" role="group">
                                        <input type="radio" class="btn-check" name="monthlyFuelType" id="monthlyRegular" value="regular" checked>
                                        <label class="btn btn-outline-primary" for="monthlyRegular">レギュラー</label>
                                        <input type="radio" class="btn-check" name="monthlyFuelType" id="monthlyPremium" value="premium">
                                        <label class="btn btn-outline-primary" for="monthlyPremium">ハイオク</label>
                                        <input type="radio" class="btn-check" name="monthlyFuelType" id="monthlyDiesel" value="diesel">
                                        <label class="btn btn-outline-primary" for="monthlyDiesel">軽油</label>
                                    </div>
                                </div>

                                <!-- ガソリン価格 -->
                                <div class="mb-3">
                                    <label class="form-label">ガソリン価格 (円/L)</label>
                                    <input type="number" class="form-control" id="monthlyGasPrice" placeholder="150" value="150">
                                </div>

                                <!-- 通勤・通学設定 -->
                                <div class="mb-3">
                                    <label class="form-label">通勤・通学設定</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="commuteDays" placeholder="20" min="0" max="31" value="20">
                                        <span class="input-group-text">日/月</span>
                                    </div>
                                    <small class="text-muted">※月の通勤・通学日数を入力</small>
                                </div>

                                <!-- 片道距離 -->
                                <div class="mb-3">
                                    <label class="form-label">片道距離 (km)</label>
                                    <input type="number" class="form-control" id="oneWayDistance" placeholder="15" value="15">
                                </div>

                                <!-- その他の移動 -->
                                <div class="mb-3">
                                    <label class="form-label">その他の移動</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="extraDistance" placeholder="100" value="100">
                                        <span class="input-group-text">km/月</span>
                                    </div>
                                    <small class="text-muted">※買い物や余暇などの移動距離</small>
                                </div>

                                <button class="btn btn-primary w-100" id="calculateMonthlyButton">月間費用を計算</button>
                            </div>
                        </div>
                        <div class="calculator-result">
                            <div id="monthlyResultDisplay">
                                <h4>月間ガソリン代試算結果</h4>
                                <div id="monthlyResultData">
                                    <p class="text-muted">入力項目を設定して計算ボタンをクリックしてください</p>
                                </div>
                                <div class="chart-container">
                                    <canvas id="monthlyChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 高速道路料金タブ -->
                <div class="tab-pane fade" id="highwayCalc">
                    <div class="calculator-box">
                        <div class="calculator-input" id="highwayCalcInput">
                            <div class="handle-bar"><div class="handle"></div></div>
                            <div class="card-body">
                                <!-- 車種区分 -->
                                <div class="mb-3">
                                    <label class="form-label">車種区分</label>
                                    <select class="form-select" id="vehicleClass">
                                        <option value="1">軽自動車等</option>
                                        <option value="2">普通車</option>
                                        <option value="3">中型車</option>
                                        <option value="4">大型車</option>
                                        <option value="5">特大車</option>
                                    </select>
                                </div>
                                
                                <!-- 主な区間選択 -->
                                <div class="mb-3">
                                    <label class="form-label">主な区間</label>
                                    <select class="form-select" id="commonRoute">
                                        <option value="">カスタム入力</option>
                                        <!-- 東京発 -->
                                        <optgroup label="東京発">
                                            <option value="tokyo-nagoya">東京-名古屋</option>
                                            <option value="tokyo-kyoto">東京-京都</option>
                                            <option value="tokyo-osaka">東京-大阪</option>
                                            <option value="tokyo-fukuoka">東京-福岡</option>
                                            <option value="tokyo-sendai">東京-仙台</option>
                                            <option value="tokyo-sapporo">東京-札幌</option>
                                            <option value="tokyo-kanazawa">東京-金沢</option>
                                            <option value="tokyo-niigata">東京-新潟</option>
                                        </optgroup>
                                        <!-- 大阪発 -->
                                        <optgroup label="大阪発">
                                            <option value="osaka-nagoya">大阪-名古屋</option>
                                            <option value="osaka-fukuoka">大阪-福岡</option>
                                            <option value="osaka-hiroshima">大阪-広島</option>
                                            <option value="osaka-kanazawa">大阪-金沢</option>
                                        </optgroup>
                                        <!-- その他 -->
                                        <optgroup label="その他">
                                            <option value="nagoya-fukuoka">名古屋-福岡</option>
                                            <option value="sendai-sapporo">仙台-札幌</option>
                                            <option value="fukuoka-kagoshima">福岡-鹿児島</option>
                                        </optgroup>
                                    </select>
                                </div>
                                
                                <!-- 離入力 -->
                                <div class="mb-3">
                                    <label class="form-label">走行距離 (km)</label>
                                    <input type="number" class="form-control" id="highwayDistance" placeholder="300" value="300">
                                </div>
                                
                                <!-- ETC割引 -->
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="etcDiscount" checked>
                                        <label class="form-check-label" for="etcDiscount">
                                            ETC割引を適用
                                        </label>
                                    </div>
                                </div>
                                
                                <button class="btn btn-primary w-100" id="calculateHighwayButton">料金を計算</button>
                            </div>
                        </div>
                        <div class="calculator-result">
                            <div id="highwayResultDisplay">
                                <h4>高速道路料金計算結果</h4>
                                <div id="highwayResultData">
                                    <p class="text-muted">入力項目を設定して計算ボタンをクリックしてください</p>
                                </div>
                                <div class="chart-container">
                                    <canvas id="highwayChart"></canvas>
                                </div>
                                <div id="routeMapContainer" class="mt-3 d-none">
                                    <h5>ルート概要</h5>
                                    <div id="routeMap" style="height: 200px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 履歴タブ -->
                <div class="tab-pane fade" id="history">
                    <div class="history-list card shadow-sm">
                        <div class="card-body">
                            <div id="calculationHistory">
                                <!-- 履歴がJavaScriptで追加される -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 計算結果 - 非表示（新しいデザインでは使わない） -->
        <div id="result" class="mt-4 d-none">
            <!-- JavaScript で結果を表示 -->
        </div>

        <!-- 詳細情報セクション -->
        <section class="mt-5">
            <h2 id="about-gas-calculation">ガソリン代計算について</h2>
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="info-card card h-100">
                        <div class="card-body">
                            <h3 class="h5">正確なガソリン代計算方法</h3>
                            <p>ガソリン代を正確に計算するには、以下の計算式を使用します：</p>
                            <ul class="list-unstyled">
                                <li>🔸 <strong>基本計算式</strong>: ガソリン代 = ガソリン価格(円/L) × (走行距離(km) ÷ 燃費(km/L))</li>
                                <li>🔸 <strong>往復の場合</strong>: 上記の計算結果 × 2</li>
                                <li>🔸 <strong>軽自動車の場合</strong>: 一般的に燃費が良いため、同じ距離でもガソリン代が安くなります</li>
                                <li>🔸 <strong>高速道路利用時</strong>: 別途料金が必要（ETCカードの利用で割引あり）</li>
                            </ul>
                            <p class="mt-3">当サイトのガソリン代計算ツールでは、これらの要素を考慮して、より正確なコスト計算が可能です。距離に応じたガソリン代の目安を簡単に算出できます。</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-card card h-100">
                        <div class="card-body">
                            <h3 class="h5">ガソリン代の節約術</h3>
                            <p>ガソリン代を節約するための実用的なヒント：</p>
                            <ul class="list-unstyled">
                                <li>💡 <strong>燃費の良い運転</strong>: エコドライブで燃費を10%以上改善可能</li>
                                <li>💡 <strong>地域別ガソリン価格の比較</strong>: 地域によって価格差があるため、安いスタンドを利用</li>
                                <li>💡 <strong>ガソリン代の割り勘計算</strong>: 相乗りの際に公平な費用分担が可能</li>
                                <li>💡 <strong>ガソリン価格アプリの活用</strong>: 最新の価格情報をチェック</li>
                                <li>💡 <strong>定期的な車のメンテナンス</strong>: エンジンオイル交換や空気圧調整で燃費向上</li>
                            </ul>
                            <p class="mt-3">当ツールを活用して、日々のガソリン代をしっかり把握し、効率的な車の利用を心がけましょう。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- よくある質問 -->
        <section class="mt-5">
            <h2 class="mb-4" id="faq">ガソリン代計算に関するよくある質問</h2>
            <div class="accordion" id="faqAccordion">
                <div class="accordion-item">
                    <h3 class="accordion-header">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                            このガソリン代計算ツールは無料で使えますか？
                        </button>
                    </h3>
                    <div id="faq1" class="accordion-collapse collapse show">
                        <div class="accordion-body">
                            はい、完全無料でご利用いただけます。会員登録も不要です。スマホやPCからアクセスするだけで、アプリのようにすぐに使えます。
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <h3 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                            ガソリン代計算の精度はどのくらいですか？
                        </button>
                    </h3>
                    <div id="faq2" class="accordion-collapse collapse">
                        <div class="accordion-body">
                            実際の走行条件（道路状況、運転方法、天候など）により変動する可能性がありますが、一般的な使用では誤差5%以内を目指しています。より正確な結果を得るために、実際の燃費データを入力することをお勧めします。特に軽自動車など車種によって燃費が大きく異なるため、正確な車種の選択が重要です。
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <h3 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                            ガソリン代計算の履歴は保存されますか？
                        </button>
                    </h3>
                    <div id="faq3" class="accordion-collapse collapse">
                        <div class="accordion-body">
                            はい、最新10件の計算履歴がブラウザに保存されます。ブラウザのデータをクリアすると履歴も削除されます。履歴機能により、定期的な移動のガソリン代を簡単に比較できます。
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <h3 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                            高速道路料金の計算にはETC割引が含まれていますか？
                        </button>
                    </h3>
                    <div id="faq4" class="accordion-collapse collapse">
                        <div class="accordion-body">
                            はい、ETCの基本割引（約10%）を適用するかどうかを選択できます。ただし、各種キャンペーンや深夜割引などの特別割引は含まれていません。高速道路料金とガソリン代を合わせた総交通費を計算できます。
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <h3 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq5">
                            スマートフォンでもガソリン代計算ツールは使えますか？
                        </button>
                    </h3>
                    <div id="faq5" class="accordion-collapse collapse">
                        <div class="accordion-body">
                            はい、スマートフォンやタブレットなど、様々な端末で快適にご利用いただけます。画面サイズに合わせて最適化されているため、専用アプリをインストールしなくても、ブラウザから簡単にアクセスできます。
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <h3 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq6">
                            地図を使ったガソリン代計算はできますか？
                        </button>
                    </h3>
                    <div id="faq6" class="accordion-collapse collapse">
                        <div class="accordion-body">
                            現在、直接地図連携機能はありませんが、Google マップなどで距離を調べてから、その数値を当ツールに入力することで正確なガソリン代を計算できます。将来的には地図連携機能の追加も検討しています。
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <h3 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq7">
                            ガソリン代の割り勘計算はどうすればいいですか？
                        </button>
                    </h3>
                    <div id="faq7" class="accordion-collapse collapse">
                        <div class="accordion-body">
                            当ツールで計算したガソリン代の合計金額を、乗車人数で割ることで簡単に割り勘金額を算出できます。例えば、往復500kmの移動でガソリン代が5,000円の場合、4人で乗車なら1人あたり1,250円となります。高速道路料金も含めた総額での割り勘も計算可能です。
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 関連記事 -->
        <section class="mt-5">
            <h2 class="mb-4" id="useful-info">ガソリン代計算に役立つ情報</h2>
            <div class="row">
                <div class="col-md-4">
                    <article class="card h-100">
                        <div class="card-body">
                            <h3 class="h5">燃費を良くする運転のコツ</h3>
                            <p>エコドライブでガソリン代を節約する方法をご紹介します：</p>
                            <ul class="list-unstyled small">
                                <li>✓ 急発進・急加速を避け、一定速度で走行する</li>
                                <li>✓ タイヤの空気圧を適正に保つ（月1回チェック推奨）</li>
                                <li>✓ 不要な荷物を積まない（10kgの荷物で約1%燃費悪化）</li>
                                <li>✓ アイドリングを控える（10分のアイドリングで約130cc消費）</li>
                                <li>✓ エアコンの使用を適切に調整する</li>
                                <li>✓ 定期的なエンジンオイル交換を行う</li>
                            </ul>
                            <p class="mt-2 small">これらの方法を実践すると、平均で10〜15%の燃費向上が期待でき、年間のガソリン代を大幅に節約できます。</p>
                        </div>
                    </article>
                </div>
                <div class="col-md-4">
                    <article class="card h-100">
                        <div class="card-body">
                            <h3 class="h5">車種別の特徴と燃費</h3>
                            <p>車種による燃費の違いとガソリン代の目安：</p>
                            <ul class="list-unstyled small">
                                <li>✓ <strong>軽自動車</strong>：20-25km/L程度（100km走行で約600円）</li>
                                <li>✓ <strong>コンパクトカー</strong>：15-20km/L（100km走行で約800円）</li>
                                <li>✓ <strong>セダン</strong>：12-15km/L（100km走行で約1,000円）</li>
                                <li>✓ <strong>ミニバン</strong>：10-15km/L（100km走行で約1,200円）</li>
                                <li>✓ <strong>SUV</strong>：8-12km/L（100km走行で約1,500円）</li>
                                <li>✓ <strong>ハイブリッド車</strong>：25-35km/L（100km走行で約450円）</li>
                            </ul>
                            <p class="mt-2 small">※ガソリン価格150円/Lの場合の概算。実際の燃費は走行条件や車の状態により変動します。</p>
                        </div>
                    </article>
                </div>
                <div class="col-md-4">
                    <article class="card h-100">
                        <div class="card-body">
                            <h3 class="h5">高速道路をお得に使う方法</h3>
                            <p>賢い高速道路の使い方とガソリン代節約：</p>
                            <ul class="list-unstyled small">
                                <li>✓ <strong>ETC割引</strong>の活用（基本約10%割引）</li>
                                <li>✓ <strong>休日割引</strong>の利用（最大30%割引）</li>
                                <li>✓ <strong>乗り継ぎ制度</strong>の活用（複数の高速道路を利用する場合）</li>
                                <li>✓ <strong>深夜割引</strong>の利用（最大30%割引、0時〜4時）</li>
                                <li>✓ <strong>定速走行</strong>による燃費改善（80-100km/hが最も効率的）</li>
                                <li>✓ <strong>SA/PAでの給油</strong>は避け、一般道のガソリンスタンドを利用</li>
                            </ul>
                            <p class="mt-2 small">高速道路の利用と適切な運転で、長距離移動時のガソリン代を効率的に抑えられます。</p>
                        </div>
                    </article>
                </div>
            </div>
            
            <!-- 新しいセクション：ガソリン代の平均と目安 -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h3 class="h5">ガソリン代の平均と距離別の目安</h3>
                            <p>一般的な車種での距離別ガソリン代の目安（ガソリン価格150円/L、燃費15km/Lの場合）：</p>
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped small">
                                    <thead>
                                        <tr>
                                            <th>距離</th>
                                            <th>軽自動車 (25km/L)</th>
                                            <th>普通車 (15km/L)</th>
                                            <th>SUV (10km/L)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>10km</td>
                                            <td>約60円</td>
                                            <td>約100円</td>
                                            <td>約150円</td>
                                        </tr>
                                        <tr>
                                            <td>50km</td>
                                            <td>約300円</td>
                                            <td>約500円</td>
                                            <td>約750円</td>
                                        </tr>
                                        <tr>
                                            <td>100km</td>
                                            <td>約600円</td>
                                            <td>約1,000円</td>
                                            <td>約1,500円</td>
                                        </tr>
                                        <tr>
                                            <td>300km</td>
                                            <td>約1,800円</td>
                                            <td>約3,000円</td>
                                            <td>約4,500円</td>
                                        </tr>
                                        <tr>
                                            <td>500km</td>
                                            <td>約3,000円</td>
                                            <td>約5,000円</td>
                                            <td>約7,500円</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <p class="small mt-2">※実際のガソリン代は、車の状態、運転方法、道路状況、ガソリン価格の変動などにより異なります。当ツールで正確な計算が可能です。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- フッター -->
    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <div class="mb-3">
                <a href="terms.html" class="text-muted mx-2">利用規約</a>
                <a href="contact.html" class="text-muted mx-2">お問い合わせ</a>
                <a href="privacy.html" class="text-muted mx-2">プライバシーポリシー</a>
            </div>
            <p class="text-muted">©2025 Masa</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <script src="js/calculator.js"></script>
</body>
</html> 
