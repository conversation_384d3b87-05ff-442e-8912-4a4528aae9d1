/**
 * Common script for the blog pages
 */

$(document).ready(function() {
    // Dark mode toggle functionality
    const darkModeToggle = $('#darkModeToggle');
    const body = $('body');
    const icon = darkModeToggle.find('i');
    
    // Check for saved dark mode preference
    const isDarkMode = localStorage.getItem('darkMode') === 'true';
    
    // Apply dark mode if previously set
    if (isDarkMode) {
        body.addClass('dark-mode');
        icon.removeClass('fa-moon').addClass('fa-sun');
    }
    
    // Toggle dark mode
    darkModeToggle.on('click', function() {
        body.toggleClass('dark-mode');
        const isDark = body.hasClass('dark-mode');
        
        // Update icon
        if (isDark) {
            icon.removeClass('fa-moon').addClass('fa-sun');
        } else {
            icon.removeClass('fa-sun').addClass('fa-moon');
        }
        
        // Save preference
        localStorage.setItem('darkMode', isDark);
    });
    
    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(event) {
        event.preventDefault();
        
        const target = $(this.getAttribute('href'));
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 800);
        }
    });
    
    // Initialize tooltips if Bootstrap is available
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}); 