const highwayRoutes = {
    'tokyo-nagoya': {
        distance: 350,
        basePrice: {
            1: 8250, // 軽自動車等
            2: 10300, // 普通車
            3: 12400, // 中型車
            4: 15500, // 大型車
            5: 17200  // 特大車
        }
    },
    // ... 其他路线数据 ...
};

function getHighwayPrice(route, vehicleClass, hasETC) {
    if (!highwayRoutes[route]) return null;
    
    const basePrice = highwayRoutes[route].basePrice[vehicleClass];
    return hasETC ? Math.round(basePrice * 0.9) : basePrice;
} 