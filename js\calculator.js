document.addEventListener('DOMContentLoaded', function() {
    // 初始化历史记录显示
    updateHistoryDisplay();
    
    // 添加计算按钮事件监听器
    const calculateButton = document.getElementById('calculateButton');
    if (calculateButton) {
        calculateButton.addEventListener('click', calculateGas);
    }
    
    // 月間試算ボタン
    const calculateMonthlyButton = document.getElementById('calculateMonthlyButton');
    if (calculateMonthlyButton) {
        calculateMonthlyButton.addEventListener('click', calculateMonthlyGas);
    }
    
    // 高速道路料金ボタン
    const calculateHighwayButton = document.getElementById('calculateHighwayButton');
    if (calculateHighwayButton) {
        calculateHighwayButton.addEventListener('click', calculateHighwayFee);
    }
    
    // ドラッグ機能の初期化
    initializeDragHandles();
    
    // 車型選択変化時の処理
    const carTypeSelect = document.getElementById('carType');
    if (carTypeSelect) {
        carTypeSelect.addEventListener('change', function(e) {
            const efficiency = document.getElementById('fuelEfficiency');
            const carTypes = {
                'custom': { efficiency: '' },
                'compact': { efficiency: 25 },
                'sedan': { efficiency: 15 },
                'suv': { efficiency: 12 }
            };
            
            if (e.target.value !== 'custom') {
                efficiency.value = carTypes[e.target.value].efficiency;
                efficiency.disabled = true;
            } else {
                efficiency.disabled = false;
                efficiency.value = '';
            }
            
            // リアルタイム計算と更新
            setTimeout(updateGasCalculation, 100);
        });
    }
    
    // 月間計算用の車型選択
    const monthlyCarTypeSelect = document.getElementById('monthlyCarType');
    if (monthlyCarTypeSelect) {
        monthlyCarTypeSelect.addEventListener('change', function(e) {
            const efficiency = document.getElementById('monthlyFuelEfficiency');
            const carTypes = {
                'custom': { efficiency: '' },
                'compact': { efficiency: 25 },
                'sedan': { efficiency: 15 },
                'suv': { efficiency: 12 }
            };
            
            if (e.target.value !== 'custom') {
                efficiency.value = carTypes[e.target.value].efficiency;
                efficiency.disabled = true;
            } else {
                efficiency.disabled = false;
                efficiency.value = '';
            }
            
            // リアルタイム計算と更新
            setTimeout(updateMonthlyCalculation, 100);
        });
    }
    
    // 入力フィールドのリアルタイム更新設定
    setupRealTimeUpdates();
    
    // ページ読み込み時に初期計算を実行
    setTimeout(function() {
        updateGasCalculation();
        updateMonthlyCalculation();
        updateHighwayCalculation();
    }, 500);

    // 添加输入验证
    const distanceInput = document.getElementById('distance');
    const fuelAmountInput = document.getElementById('fuelAmount');
    
    if (distanceInput && fuelAmountInput) {
        [distanceInput, fuelAmountInput].forEach(input => {
            input.addEventListener('input', function() {
                this.value = this.value.replace(/[^0-9.]/g, '');
            });
        });
    }
});

// ドラッグハンドル機能の初期化
function initializeDragHandles() {
    const draggableInputs = [
        { input: document.getElementById('gasCalcInput'), container: document.querySelector('#gasCalc .calculator-box') },
        { input: document.getElementById('monthlyCalcInput'), container: document.querySelector('#monthlyCalc .calculator-box') },
        { input: document.getElementById('highwayCalcInput'), container: document.querySelector('#highwayCalc .calculator-box') }
    ];
    
    draggableInputs.forEach(item => {
        if (!item.input || !item.container) return;
        
        const handle = item.input.querySelector('.handle-bar');
        if (!handle) return;
        
        let isResizing = false;
        let startX, startWidth;
        
        handle.addEventListener('mousedown', function(e) {
            isResizing = true;
            startX = e.clientX;
            startWidth = item.input.offsetWidth;
            document.body.style.cursor = 'col-resize';
            e.preventDefault();
        });
        
        document.addEventListener('mousemove', function(e) {
            if (!isResizing) return;
            
            const containerWidth = item.container.offsetWidth;
            const newWidth = startWidth + (e.clientX - startX);
            const minWidth = 280; // 最小幅
            const maxWidth = containerWidth * 0.7; // 最大幅（コンテナの70%）
            
            const clampedWidth = Math.max(minWidth, Math.min(newWidth, maxWidth));
            const flexBasis = (clampedWidth / containerWidth) * 100;
            
            item.input.style.flex = `0 0 ${flexBasis}%`;
        });
        
        document.addEventListener('mouseup', function() {
            if (isResizing) {
                isResizing = false;
                document.body.style.cursor = '';
            }
        });
    });
}

// リアルタイム更新のためのイベントリスナー設定
function setupRealTimeUpdates() {
    // ガソリン代計算の入力フィールド
    const gasInputs = ['gasPrice', 'distance', 'fuelEfficiency'];
    gasInputs.forEach(id => {
        const input = document.getElementById(id);
        if (input) {
            input.addEventListener('input', updateGasCalculation);
        }
    });
    
    const roundTripCheckbox = document.getElementById('roundTrip');
    if (roundTripCheckbox) {
        roundTripCheckbox.addEventListener('change', updateGasCalculation);
    }
    
    // 月間計算の入力フィールド
    const monthlyInputs = ['monthlyFuelEfficiency', 'monthlyGasPrice', 'commuteDays', 'oneWayDistance', 'extraDistance'];
    monthlyInputs.forEach(id => {
        const input = document.getElementById(id);
        if (input) {
            input.addEventListener('input', updateMonthlyCalculation);
        }
    });
    
    // 燃料タイプのラジオボタン
    const fuelTypeRadios = document.querySelectorAll('input[name="monthlyFuelType"]');
    fuelTypeRadios.forEach(radio => {
        radio.addEventListener('change', updateMonthlyCalculation);
    });
    
    // 高速道路料金の入力フィールド
    const highwayInputs = ['vehicleClass', 'highwayDistance', 'etcDiscount', 'commonRoute'];
    highwayInputs.forEach(id => {
        const input = document.getElementById(id);
        if (input) {
            input.addEventListener('change', updateHighwayCalculation);
            input.addEventListener('input', updateHighwayCalculation);
        }
    });
}

// ガソリン代計算のリアルタイム更新
function updateGasCalculation() {
    const gasPrice = parseFloat(document.getElementById('gasPrice').value) || 0;
    const distance = parseFloat(document.getElementById('distance').value) || 0;
    const fuelEfficiency = parseFloat(document.getElementById('fuelEfficiency').value) || 0;
    const isRoundTrip = document.getElementById('roundTrip').checked;
    
    if (gasPrice <= 0 || distance <= 0 || fuelEfficiency <= 0) {
        document.getElementById('resultData').innerHTML = `
            <p class="text-muted">全ての項目に有効な値を入力してください</p>
        `;
        return;
    }
    
    // 計算
    const totalDistance = isRoundTrip ? distance * 2 : distance;
    const fuelNeeded = totalDistance / fuelEfficiency;
    const totalCost = fuelNeeded * gasPrice;
    
    // 結果表示
    document.getElementById('resultData').innerHTML = `
        <div class="result-summary">
            <div class="row">
                <div class="col-6">
                    <p><strong>総距離:</strong> ${totalDistance.toFixed(1)} km</p>
                    <p><strong>必要な燃料:</strong> ${fuelNeeded.toFixed(1)} L</p>
                </div>
                <div class="col-6">
                    <p><strong>ガソリン価格:</strong> ${gasPrice.toFixed(0)} 円/L</p>
                    <p><strong>総費用:</strong> ${totalCost.toFixed(0)} 円</p>
                </div>
            </div>
        </div>
    `;
    
    // グラフ更新
    updateGasChart(gasPrice, fuelNeeded, totalCost);
}

// 月間ガソリン代計算のリアルタイム更新
function updateMonthlyCalculation() {
    const fuelEfficiency = parseFloat(document.getElementById('monthlyFuelEfficiency').value) || 0;
    const gasPrice = parseFloat(document.getElementById('monthlyGasPrice').value) || 0;
    const commuteDays = parseFloat(document.getElementById('commuteDays').value) || 0;
    const oneWayDistance = parseFloat(document.getElementById('oneWayDistance').value) || 0;
    const extraDistance = parseFloat(document.getElementById('extraDistance').value) || 0;
    
    if (fuelEfficiency <= 0 || gasPrice <= 0 || (commuteDays <= 0 && extraDistance <= 0)) {
        document.getElementById('monthlyResultData').innerHTML = `
            <p class="text-muted">全ての項目に有効な値を入力してください</p>
        `;
        return;
    }
    
    // 計算
    const commuteDistance = oneWayDistance * 2 * commuteDays;
    const totalDistance = commuteDistance + extraDistance;
    const fuelNeeded = totalDistance / fuelEfficiency;
    const totalCost = fuelNeeded * gasPrice;
    
    // 結果表示
    document.getElementById('monthlyResultData').innerHTML = `
        <div class="result-summary">
            <div class="row">
                <div class="col-6">
                    <p><strong>通勤距離:</strong> ${commuteDistance.toFixed(1)} km/月</p>
                    <p><strong>その他距離:</strong> ${extraDistance.toFixed(1)} km/月</p>
                </div>
                <div class="col-6">
                    <p><strong>必要な燃料:</strong> ${fuelNeeded.toFixed(1)} L/月</p>
                    <p><strong>月間費用:</strong> ${totalCost.toFixed(0)} 円</p>
                </div>
            </div>
            <div class="mt-2">
                <p><strong>年間総費用:</strong> ${(totalCost * 12).toFixed(0)} 円</p>
            </div>
        </div>
    `;
    
    // グラフ更新
    updateMonthlyChart(commuteDistance, extraDistance, totalCost);
}

// 高速道路料金計算のリアルタイム更新
function updateHighwayCalculation() {
    const vehicleClass = document.getElementById('vehicleClass').value;
    const distance = parseFloat(document.getElementById('highwayDistance').value) || 0;
    const hasETC = document.getElementById('etcDiscount').checked;
    const commonRoute = document.getElementById('commonRoute').value;
    
    if (distance <= 0) {
        document.getElementById('highwayResultData').innerHTML = `
            <p class="text-muted">有効な走行距離を入力してください</p>
        `;
        return;
    }
    
    // 基本費率（円/km）
    const baseRates = {
        '1': 24.6, // 軽自動車等
        '2': 29.52, // 普通車
        '3': 36.9, // 中型車
        '4': 49.2, // 大型車
        '5': 82.0  // 特大車
    };
    
    // 主な区間の距離と料金
    let routeInfo = null;
    if (commonRoute) {
        const routes = {
            'tokyo-nagoya': { distance: 350, baseFee: 7600 },
            'tokyo-kyoto': { distance: 450, baseFee: 10300 },
            'tokyo-osaka': { distance: 500, baseFee: 11400 },
            'tokyo-fukuoka': { distance: 1100, baseFee: 25500 },
            'tokyo-sendai': { distance: 350, baseFee: 7400 },
            'tokyo-sapporo': { distance: 1050, baseFee: 24800 },
            'tokyo-kanazawa': { distance: 470, baseFee: 10400 },
            'tokyo-niigata': { distance: 340, baseFee: 7200 },
            'osaka-nagoya': { distance: 190, baseFee: 4300 },
            'osaka-fukuoka': { distance: 600, baseFee: 14500 },
            'osaka-hiroshima': { distance: 330, baseFee: 7400 },
            'osaka-kanazawa': { distance: 210, baseFee: 4800 },
            'nagoya-fukuoka': { distance: 740, baseFee: 17800 },
            'sendai-sapporo': { distance: 700, baseFee: 17400 },
            'fukuoka-kagoshima': { distance: 250, baseFee: 5900 }
        };
        
        routeInfo = routes[commonRoute];
        if (routeInfo) {
            document.getElementById('highwayDistance').value = routeInfo.distance;
        }
    }
    
    // 計算基本费用
    let baseFee;
    if (routeInfo) {
        // 区間料金をベースに車両区分に合わせて調整
        const vehicleMultiplier = {
            '1': 0.8,  // 軽自動車は約20%安い
            '2': 1,    // 普通車は基準
            '3': 1.2,  // 中型車は約20%高い
            '4': 1.5,  // 大型車は約50%高い
            '5': 2.0   // 特大車は約2倍
        };
        baseFee = routeInfo.baseFee * vehicleMultiplier[vehicleClass];
    } else {
        // カスタム距離の場合
        baseFee = distance * baseRates[vehicleClass];
    }
    
    // 应用ETC折扣
    let finalFee = baseFee;
    if (hasETC) {
        finalFee = baseFee * 0.9; // 10%割引
    }
    
    // 表示
    const vehicleClassName = getVehicleClassName(vehicleClass);
    document.getElementById('highwayResultData').innerHTML = `
        <div class="result-summary">
            <div class="row">
                <div class="col-6">
                    <p><strong>車種区分:</strong> ${vehicleClassName}</p>
                    <p><strong>走行距離:</strong> ${distance} km</p>
                </div>
                <div class="col-6">
                    <p><strong>基本料金:</strong> ${Math.round(baseFee)} 円</p>
                    <p><strong>${hasETC ? 'ETC割引後' : '最終'}料金:</strong> ${Math.round(finalFee)} 円</p>
                </div>
            </div>
            ${commonRoute ? `<div class="mt-2"><p><strong>区間:</strong> ${commonRoute.replace('-', ' → ')}</p></div>` : ''}
        </div>
    `;
    
    // グラフ更新
    updateHighwayChart(baseFee, finalFee, vehicleClassName);
}

// ガソリン代計算のグラフ更新
function updateGasChart(gasPrice, fuelNeeded, totalCost) {
    const ctx = document.getElementById('gasChart').getContext('2d');
    
    // 既存のチャートを破棄
    if (window.gasChartInstance) {
        window.gasChartInstance.destroy();
    }
    
    // 横棒グラフでコスト内訳を表示
    window.gasChartInstance = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['ガソリン代内訳'],
            datasets: [{
                label: '燃料費',
                data: [totalCost],
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `燃料 ${fuelNeeded.toFixed(1)}L (${gasPrice}円/L): ${totalCost.toFixed(0)}円`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '金額 (円)'
                    }
                }
            }
        }
    });
}

// 月間ガソリン代計算のグラフ更新
function updateMonthlyChart(commuteDistance, extraDistance, totalCost) {
    const ctx = document.getElementById('monthlyChart').getContext('2d');
    
    // 既存のチャートを破棄
    if (window.monthlyChartInstance) {
        window.monthlyChartInstance.destroy();
    }
    
    // 距離の内訳を円グラフで表示
    const totalDistance = commuteDistance + extraDistance;
    const commutePercentage = (commuteDistance / totalDistance) * 100;
    const extraPercentage = (extraDistance / totalDistance) * 100;
    
    window.monthlyChartInstance = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['通勤・通学', 'その他の移動'],
            datasets: [{
                data: [commuteDistance, extraDistance],
                backgroundColor: [
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 99, 132, 0.7)'
                ],
                borderColor: [
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 99, 132, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const percentage = ((value / totalDistance) * 100).toFixed(1);
                            return `${label}: ${value.toFixed(1)}km (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// 高速道路料金計算のグラフ更新
function updateHighwayChart(baseFee, finalFee, vehicleClassName) {
    const ctx = document.getElementById('highwayChart').getContext('2d');
    
    // 既存のチャートを破棄
    if (window.highwayChartInstance) {
        window.highwayChartInstance.destroy();
    }
    
    // ETC割引の有無による比較グラフ
    window.highwayChartInstance = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['基本料金', 'ETC割引適用'],
            datasets: [{
                label: `${vehicleClassName}料金`,
                data: [baseFee, finalFee],
                backgroundColor: [
                    'rgba(255, 159, 64, 0.7)',
                    'rgba(75, 192, 192, 0.7)'
                ],
                borderColor: [
                    'rgba(255, 159, 64, 1)',
                    'rgba(75, 192, 192, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.dataset.label}: ${Math.round(context.raw)}円`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '料金 (円)'
                    }
                }
            }
        }
    });
}

// 修改历史记录显示函数，添加错误处理
function updateHistoryDisplay() {
    const historyDiv = document.getElementById('calculationHistory');
    if (!historyDiv) return; // 如果元素不存在，直接返回
    
    try {
        const history = JSON.parse(localStorage.getItem('calculationHistory') || '[]');
        
        if (history.length === 0) {
            historyDiv.innerHTML = '<p class="text-muted">計算履歴はありません。</p>';
            return;
        }

        const historyHTML = history.map((calc, index) => {
            // 添加数据验证
            const distance = typeof calc.distance === 'number' ? calc.distance.toFixed(1) : '0.0';
            const fuelNeeded = typeof calc.fuelNeeded === 'number' ? calc.fuelNeeded.toFixed(1) : '0.0';
            const cost = typeof calc.cost === 'number' ? calc.cost.toFixed(0) : '0';
            const date = calc.date ? new Date(calc.date).toLocaleString() : '不明';

            return `
                <div class="card mb-2">
                    <div class="card-body">
                        <h5 class="card-title">計算 ${index + 1}</h5>
                        <p class="card-text">
                            距離: ${distance} km<br>
                            燃料: ${fuelNeeded} L<br>
                            費用: ${cost} 円<br>
                            日時: ${date}
                        </p>
                    </div>
                </div>
            `;
        }).join('');

        historyDiv.innerHTML = historyHTML;
    } catch (error) {
        console.error('Error updating history display:', error);
        historyDiv.innerHTML = '<p class="text-muted">履歴の読み込みに失敗しました。</p>';
    }
}

// ガソリン代計算関数の修正
function calculateGas() {
    // 获取输入值
    const gasPrice = parseFloat(document.getElementById('gasPrice').value);
    const distance = parseFloat(document.getElementById('distance').value);
    const fuelEfficiency = parseFloat(document.getElementById('fuelEfficiency').value);
    const isRoundTrip = document.getElementById('roundTrip').checked;

    // 验证输入
    if (!validateInputs(gasPrice, distance, fuelEfficiency)) {
        return;
    }

    // 计算总距离
    const totalDistance = isRoundTrip ? distance * 2 : distance;
    
    // 计算所需燃料量
    const fuelNeeded = totalDistance / fuelEfficiency;
    
    // 计算总成本
    const totalCost = fuelNeeded * gasPrice;

    // 显示结果
    const resultDiv = document.getElementById('result');
    resultDiv.innerHTML = `
        <div class="alert alert-success">
            <h4 class="alert-heading">計算結果</h4>
            <p>総距離: ${totalDistance.toFixed(1)} km</p>
            <p>必要な燃料: ${fuelNeeded.toFixed(1)} L</p>
            <p>総費用: ${totalCost.toFixed(0)} 円</p>
        </div>
    `;
    resultDiv.classList.remove('d-none');

    // 保存到历史记录
    saveToHistory({
        type: 'gas',
        distance: totalDistance,
        fuelNeeded: fuelNeeded,
        cost: totalCost,
        date: new Date()
    });
}

// 保存计算历史
function saveToHistory(calculation) {
    let history = JSON.parse(localStorage.getItem('calculationHistory') || '[]');
    history.unshift(calculation);
    history = history.slice(0, 10); // 只保留最近10条记录
    localStorage.setItem('calculationHistory', JSON.stringify(history));
    updateHistoryDisplay();
}

// 输入验证函数
function validateInputs(price, distance, efficiency) {
    if (isNaN(price) || price <= 0) {
        showError('ガソリン価格を正しく入力してください。');
        return false;
    }
    if (isNaN(distance) || distance <= 0) {
        showError('走行距離を正しく入力してください。');
        return false;
    }
    if (isNaN(efficiency) || efficiency <= 0) {
        showError('燃費を正しく入力してください。');
        return false;
    }
    return true;
}

// 错误显示函数
function showError(message) {
    const resultDiv = document.getElementById('result');
    resultDiv.innerHTML = `
        <div class="alert alert-danger">
            <p>${message}</p>
        </div>
    `;
    resultDiv.classList.remove('d-none');
}

// 添加月間試算の計算機能
function calculateMonthlyGas() {
    // 获取输入值
    const fuelEfficiency = parseFloat(document.getElementById('monthlyFuelEfficiency').value);
    const gasPrice = parseFloat(document.getElementById('monthlyGasPrice').value);
    const commuteDays = parseFloat(document.getElementById('commuteDays').value);
    const oneWayDistance = parseFloat(document.getElementById('oneWayDistance').value);
    const extraDistance = parseFloat(document.getElementById('extraDistance').value) || 0;

    // 验证输入
    if (!validateMonthlyInputs(fuelEfficiency, gasPrice, commuteDays, oneWayDistance)) {
        return;
    }

    // 计算
    const commuteDistance = oneWayDistance * 2 * commuteDays;
    const totalDistance = commuteDistance + extraDistance;
    const fuelNeeded = totalDistance / fuelEfficiency;
    const totalCost = fuelNeeded * gasPrice;

    // 显示结果
    const resultDiv = document.getElementById('result');
    resultDiv.innerHTML = `
        <div class="alert alert-success">
            <h4 class="alert-heading">月間コスト試算結果</h4>
            <div class="row">
                <div class="col-md-6">
                    <p>通勤・通学距離: ${commuteDistance.toFixed(1)} km/月</p>
                    <p>その他の移動距離: ${extraDistance.toFixed(1)} km/月</p>
                </div>
                <div class="col-md-6">
                    <p>必要な燃料: ${fuelNeeded.toFixed(1)} L/月</p>
                    <p>月間費用: ${totalCost.toFixed(0)} 円</p>
                </div>
            </div>
        </div>
    `;
    resultDiv.classList.remove('d-none');

    // 保存到历史记录
    saveToHistory({
        type: 'monthly',
        distance: totalDistance,
        fuelNeeded: fuelNeeded,
        cost: totalCost,
        date: new Date()
    });
}

// 添加高速道路料金計算機能
function calculateHighwayFee() {
    // 获取输入值
    const vehicleClass = document.getElementById('vehicleClass').value;
    const distance = parseFloat(document.getElementById('highwayDistance').value);
    const hasETC = document.getElementById('etcDiscount').checked;
    const commonRoute = document.getElementById('commonRoute').value;

    // 验证输入
    if (!validateHighwayInputs(distance)) {
        return;
    }

    // 基本费率（円/km）
    const baseRates = {
        '1': 24.6, // 軽自動車等
        '2': 29.52, // 普通車
        '3': 36.9, // 中型車
        '4': 49.2, // 大型車
        '5': 82.0  // 特大車
    };

    // 计算基本费用
    let baseFee = distance * baseRates[vehicleClass];
    
    // 应用ETC折扣
    if (hasETC) {
        baseFee *= 0.9; // 10%折扣
    }

    // 添加终端费用
    baseFee += 150; // 终端费用

    // 显示结果
    const resultDiv = document.getElementById('result');
    resultDiv.innerHTML = `
        <div class="alert alert-success">
            <h4 class="alert-heading">高速道路料金試算結果</h4>
            <div class="row">
                <div class="col-md-6">
                    <p>走行距離: ${distance.toFixed(1)} km</p>
                    <p>車種区分: ${getVehicleClassName(vehicleClass)}</p>
                </div>
                <div class="col-md-6">
                    <p>ETC割引: ${hasETC ? '適用' : '未適用'}</p>
                    <p>概算料金: ${Math.round(baseFee)} 円</p>
                </div>
            </div>
            <small class="text-muted">※実際の料金は区間や時間帯により異なる場合があります。</small>
        </div>
    `;
    resultDiv.classList.remove('d-none');

    // 保存到历史记录
    saveToHistory({
        type: 'highway',
        distance: distance,
        vehicleClass: getVehicleClassName(vehicleClass),
        cost: Math.round(baseFee),
        date: new Date()
    });
}

// 验证月間試算输入
function validateMonthlyInputs(efficiency, price, days, distance) {
    if (isNaN(efficiency) || efficiency <= 0) {
        showError('燃費を正しく入力してください。');
        return false;
    }
    if (isNaN(price) || price <= 0) {
        showError('ガソリン価格を正しく入力してください。');
        return false;
    }
    if (isNaN(days) || days <= 0 || days > 31) {
        showError('通勤・通学日数を正しく入力してください。');
        return false;
    }
    if (isNaN(distance) || distance <= 0) {
        showError('片道距離を正しく入力してください。');
        return false;
    }
    return true;
}

// 验证高速道路料金输入
function validateHighwayInputs(distance) {
    if (isNaN(distance) || distance <= 0) {
        showError('走行距離を正しく入力してください。');
        return false;
    }
    return true;
}

// 获取车辆类型名称
function getVehicleClassName(classId) {
    const classNames = {
        '1': '軽自動車等',
        '2': '普通車',
        '3': '中型車',
        '4': '大型車',
        '5': '特大車'
    };
    return classNames[classId] || '不明';
}

// 添加交通費計算機能
function calculateTrafficCost() {
    // 获取基本输入
    const transportType = document.querySelector('input[name="transportType"]:checked').value;
    const distance = parseFloat(document.getElementById('distance').value);
    const isRoundTrip = document.getElementById('roundTrip').checked;

    // 验证输入
    if (!validateTrafficInputs(distance)) {
        return;
    }

    let totalCost = 0;

    // 根据交通方式计算
    switch (transportType) {
        case 'train':
            totalCost = calculateTrainFare(distance);
            break;
        case 'bus':
            totalCost = calculateBusFare(distance);
            break;
        case 'highway':
            totalCost = calculateHighwayFare(distance);
            break;
    }

    // 如果是往复，费用翻倍
    if (isRoundTrip) {
        totalCost *= 2;
    }

    // 显示结果
    displayTrafficResult(transportType, distance, totalCost, isRoundTrip);

    // 保存到历史记录
    saveToHistory({
        type: 'traffic',
        transportType: transportType,
        distance: isRoundTrip ? distance * 2 : distance,
        cost: totalCost,
        date: new Date()
    });
}

// 计算电车费用
function calculateTrainFare(distance) {
    let baseFare = 0;
    const seasonSelect = document.getElementById('seasonSelect').value;
    const useExpress = document.getElementById('expressOption').checked;
    const useReservedSeat = document.getElementById('reservedSeatOption').checked;
    const useGreenCar = document.getElementById('greenCarOption').checked;

    // 基本运费计算
    if (distance <= 3) {
        baseFare = 150;
    } else if (distance <= 6) {
        baseFare = 190;
    } else if (distance <= 10) {
        baseFare = 200;
    } else if (distance <= 15) {
        baseFare = 260;
    } else {
        baseFare = 260 + Math.ceil((distance - 15) / 5) * 40;
    }

    // 季节性调整
    switch (seasonSelect) {
        case 'peak':
            baseFare *= 1.2; // 旺季加价20%
            break;
        case 'offPeak':
            baseFare *= 0.9; // 淡季优惠10%
            break;
    }

    // 特急料金
    if (useExpress) {
        baseFare += 950 + (distance * 15);
    }

    // 指定席料金
    if (useReservedSeat) {
        baseFare += distance > 600 ? 1040 : 520;
    }

    // グリーン車料金
    if (useGreenCar) {
        baseFare += distance > 600 ? 3300 : 2200;
    }

    return baseFare;
}

// 计算巴士费用
function calculateBusFare(distance) {
    // 基本运费：20円/km，最低220円
    let fare = Math.max(220, distance * 20);
    
    // 如果是深夜时段（可以添加时间检查逻辑）
    const isNightTime = false; // 这里可以添加时间判断
    if (isNightTime) {
        fare *= 1.3; // 深夜加价30%
    }

    return Math.round(fare);
}

// 计算高速公路费用
function calculateHighwayFare(distance) {
    let fare = 0;
    const hasETC = document.getElementById('etcOption').checked;
    const isHoliday = document.getElementById('holidayOption').checked;
    const isNightTime = document.getElementById('nightTimeOption').checked;

    // 基本费率计算
    if (distance <= 100) {
        fare = distance * 24.6;
    } else if (distance <= 200) {
        fare = distance * 23.37; // 5%折扣
    } else if (distance <= 500) {
        fare = distance * 22.14; // 10%折扣
    } else {
        fare = distance * 20.91; // 15%折扣
    }

    // 应用ETC折扣
    if (hasETC) {
        if (isHoliday) {
            fare *= 0.7; // 休日割引30%
        } else if (isNightTime) {
            fare *= 0.5; // 深夜割引50%
        } else {
            fare *= 0.9; // 基本ETC折扣10%
        }
    }

    // 添加终端费用
    fare += 150;

    return Math.round(fare);
}

// 显示交通费计算结果
function displayTrafficResult(transportType, distance, cost, isRoundTrip) {
    const resultDiv = document.getElementById('result');
    const transportNames = {
        'train': '電車',
        'bus': 'バス',
        'highway': '高速道路'
    };

    resultDiv.innerHTML = `
        <div class="alert alert-success">
            <h4 class="alert-heading">計算結果</h4>
            <div class="row">
                <div class="col-md-6">
                    <p>交通手段: ${transportNames[transportType]}</p>
                    <p>距離: ${distance} km ${isRoundTrip ? '(往復)' : '(片道)'}</p>
                </div>
                <div class="col-md-6">
                    <p>料金: ${cost.toLocaleString()} 円</p>
                    ${isRoundTrip ? `<p>片道料金: ${(cost / 2).toLocaleString()} 円</p>` : ''}
                </div>
            </div>
        </div>
    `;
    resultDiv.classList.remove('d-none');
}

// 验证交通费输入
function validateTrafficInputs(distance) {
    if (isNaN(distance) || distance <= 0) {
        showError('距離を正しく入力してください。');
        return false;
    }
    return true;
}

// 交通手段切换时显示/隐藏相应选项
document.addEventListener('DOMContentLoaded', function() {
    const transportTypes = document.getElementsByName('transportType');
    const trainOptions = document.getElementById('trainOptions');
    const highwayOptions = document.getElementById('highwayOptions');

    transportTypes.forEach(type => {
        type.addEventListener('change', function() {
            trainOptions.style.display = this.value === 'train' ? 'block' : 'none';
            highwayOptions.style.display = this.value === 'highway' ? 'block' : 'none';
        });
    });
});

// 修改燃費計算機能
function calculateFuelEfficiency() {
    // 获取输入值 - 更新为与 HTML 匹配的 ID
    const previousFuel = parseFloat(document.getElementById('previousFuel').value);
    const currentFuel = parseFloat(document.getElementById('currentFuel').value);
    const distance = parseFloat(document.getElementById('distance').value);
    const drivingCondition = document.querySelector('input[name="drivingCondition"]:checked').value;
    const airConUsed = document.getElementById('airConUsed').checked;
    const heavyLoad = document.getElementById('heavyLoad').checked;

    // 验证输入
    if (!validateFuelInputs(distance, previousFuel, currentFuel)) {
        return;
    }

    // 计算实际使用的燃料量
    const fuelAmount = currentFuel;
    
    // 计算燃费
    const efficiency = distance / fuelAmount;

    // 获取影响因素
    let factors = [];
    if (airConUsed) {
        factors.push('エアコン使用');
    }
    if (heavyLoad) {
        factors.push('重い荷物を積載');
    }

    // 获取走行条件说明
    const conditionText = {
        'city': '市街地走行',
        'highway': '高速道路走行',
        'mixed': '混合走行'
    }[drivingCondition];

    // 显示结果
    const resultDiv = document.getElementById('result');
    resultDiv.innerHTML = `
        <div class="alert alert-success">
            <h4 class="alert-heading">燃費計算結果</h4>
            <div class="row">
                <div class="col-md-6">
                    <p>走行距離: ${distance.toFixed(1)} km</p>
                    <p>使用燃料: ${fuelAmount.toFixed(1)} L</p>
                    <p>走行条件: ${conditionText}</p>
                </div>
                <div class="col-md-6">
                    <p>燃費: ${efficiency.toFixed(1)} km/L</p>
                    ${factors.length > 0 ? `<p>影響要因: ${factors.join('、')}</p>` : ''}
                </div>
            </div>
            <div class="mt-3">
                <h5>燃費の評価</h5>
                ${getFuelEfficiencyEvaluation(efficiency)}
            </div>
        </div>
    `;
    resultDiv.classList.remove('d-none');

    // 保存到历史记录
    saveToHistory({
        type: 'fuel',
        distance: distance,
        fuelAmount: fuelAmount,
        efficiency: efficiency,
        condition: conditionText,
        factors: factors,
        date: new Date()
    });

    // 更新图表
    updateFuelEfficiencyChart();
}

// 修改验证函数
function validateFuelInputs(distance, previousFuel, currentFuel) {
    if (isNaN(distance) || distance <= 0) {
        showError('走行距離を正しく入力してください。');
        return false;
    }
    if (isNaN(previousFuel) || previousFuel < 0) {
        showError('前回の給油量を正しく入力してください。');
        return false;
    }
    if (isNaN(currentFuel) || currentFuel <= 0) {
        showError('今回の給油量を正しく入力してください。');
        return false;
    }
    return true;
}

// 添加图表更新函数
function updateFuelEfficiencyChart() {
    const ctx = document.getElementById('fuelEfficiencyChart');
    if (!ctx) return;

    // 获取历史数据
    const history = JSON.parse(localStorage.getItem('calculationHistory') || '[]')
        .filter(item => item.type === 'fuel')
        .slice(-10); // 最近10条记录

    const labels = history.map(item => {
        const date = new Date(item.date);
        return `${date.getMonth() + 1}/${date.getDate()}`;
    });

    const data = history.map(item => item.efficiency);

    // 如果已经存在图表，先销毁
    if (window.fuelChart) {
        window.fuelChart.destroy();
    }

    // 创建新图表
    window.fuelChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '燃費 (km/L)',
                data: data,
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '燃費 (km/L)'
                    }
                }
            },
            plugins: {
                title: {
                    display: true,
                    text: '燃費の推移'
                }
            }
        }
    });
}

// 获取燃费评价
function getFuelEfficiencyEvaluation(efficiency) {
    let evaluation = '';
    if (efficiency >= 20) {
        evaluation = `
            <div class="text-success">
                <i class="fas fa-star"></i> 非常に良好な燃費です！
                <p class="small mt-2">エコドライブが実践できています。この調子で続けましょう。</p>
            </div>
        `;
    } else if (efficiency >= 15) {
        evaluation = `
            <div class="text-primary">
                <i class="fas fa-thumbs-up"></i> 平均的な燃費です。
                <p class="small mt-2">さらなる改善の余地があります。エコドライブを心がけましょう。</p>
            </div>
        `;
    } else {
        evaluation = `
            <div class="text-warning">
                <i class="fas fa-exclamation-triangle"></i> 改善の余地があります。
                <p class="small mt-2">
                    以下のポイントを意識してみましょう：<br>
                    ・急発進、急加速を避ける<br>
                    ・タイヤの空気圧を適正に保つ<br>
                    ・不要な荷物を減らす
                </p>
            </div>
        `;
    }
    return evaluation;
}
  