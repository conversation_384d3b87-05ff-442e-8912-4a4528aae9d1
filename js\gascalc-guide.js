/**
 * ガソリン代計算ガイドのJavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // スムーズスクロール
    setupSmoothScroll();
    
    // 目次のアクティブ状態管理
    setupTocHighlighting();
    
    // 計算例のインタラクション
    setupCalculationExamples();
    
    // サイドバーの固定
    setupStickyElement('.sidebar');
    
    // 画像の拡大表示
    setupImageZoom();
});

/**
 * スムーズスクロールの設定
 */
function setupSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                const offsetTop = targetElement.getBoundingClientRect().top + window.pageYOffset - 80;
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
                
                // URLハッシュを更新（履歴に追加せず）
                history.replaceState(null, null, targetId);
            }
        });
    });
}

/**
 * 目次の現在地ハイライト設定
 */
function setupTocHighlighting() {
    if (!document.querySelector('.toc-container')) return;
    
    const sections = document.querySelectorAll('h2[id], h3[id]');
    const tocLinks = document.querySelectorAll('.toc-container a');
    
    const observerOptions = {
        root: null,
        rootMargin: '-100px 0px -80% 0px',
        threshold: 0
    };
    
    const headingObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const activeId = entry.target.getAttribute('id');
                
                // 以前のアクティブリンクからクラスを削除
                tocLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === `#${activeId}`) {
                        link.classList.add('active');
                    }
                });
            }
        });
    }, observerOptions);
    
    sections.forEach(section => {
        headingObserver.observe(section);
    });
}

/**
 * 計算例のインタラクション
 */
function setupCalculationExamples() {
    const examples = document.querySelectorAll('.interactive-example');
    
    examples.forEach(example => {
        const inputs = example.querySelectorAll('input');
        const calculateButton = example.querySelector('.calculate-button');
        const resultElement = example.querySelector('.calculation-result');
        
        if (calculateButton && resultElement) {
            calculateButton.addEventListener('click', () => {
                // データの収集
                const data = {};
                inputs.forEach(input => {
                    data[input.name] = parseFloat(input.value) || 0;
                });
                
                // 計算ロジック：距離÷燃費×ガソリン価格
                const distance = data.distance || 0;
                const fuelEfficiency = data.fuelEfficiency || 1; // 0除算を避ける
                const fuelPrice = data.fuelPrice || 0;
                
                const fuelAmount = distance / fuelEfficiency;
                const cost = fuelAmount * fuelPrice;
                
                // 結果の表示
                if (resultElement) {
                    resultElement.innerHTML = `
                        <div class="alert alert-success mt-3">
                            <p><strong>計算結果:</strong></p>
                            <p>必要燃料: ${fuelAmount.toFixed(2)} リットル</p>
                            <p>ガソリン代: ${cost.toFixed(0)} 円</p>
                        </div>
                    `;
                }
            });
        }
    });
}

/**
 * 要素の固定表示
 */
function setupStickyElement(selector) {
    const element = document.querySelector(selector);
    if (!element) return;
    
    const originalTop = element.getBoundingClientRect().top + window.pageYOffset;
    const footer = document.querySelector('footer');
    
    function updateStickyPosition() {
        const footerTop = footer.getBoundingClientRect().top;
        const windowHeight = window.innerHeight;
        const elementHeight = element.offsetHeight;
        
        if (window.pageYOffset > originalTop - 90) {
            element.classList.add('position-fixed');
            element.style.top = '90px';
            element.style.width = element.parentElement.offsetWidth + 'px';
            
            // フッターが見えたら固定を解除
            if (footerTop < windowHeight) {
                const diff = windowHeight - footerTop;
                element.style.top = (90 - diff) + 'px';
            }
        } else {
            element.classList.remove('position-fixed');
            element.style.top = '';
            element.style.width = '';
        }
    }
    
    if (window.innerWidth >= 992) {
        window.addEventListener('scroll', updateStickyPosition);
        window.addEventListener('resize', () => {
            if (window.innerWidth >= 992) {
                updateStickyPosition();
                element.style.width = element.parentElement.offsetWidth + 'px';
            } else {
                element.classList.remove('position-fixed');
                element.style.top = '';
                element.style.width = '';
            }
        });
    }
}

/**
 * 画像のクリックで拡大表示
 */
function setupImageZoom() {
    const contentImages = document.querySelectorAll('.blog-content img:not(.no-zoom)');
    
    contentImages.forEach(img => {
        img.style.cursor = 'pointer';
        
        img.addEventListener('click', () => {
            // モーダル作成
            const modal = document.createElement('div');
            modal.classList.add('image-zoom-modal');
            modal.style.position = 'fixed';
            modal.style.top = '0';
            modal.style.left = '0';
            modal.style.width = '100%';
            modal.style.height = '100%';
            modal.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
            modal.style.display = 'flex';
            modal.style.alignItems = 'center';
            modal.style.justifyContent = 'center';
            modal.style.zIndex = '1000';
            
            // 拡大画像
            const zoomedImg = document.createElement('img');
            zoomedImg.src = img.src;
            zoomedImg.style.maxWidth = '90%';
            zoomedImg.style.maxHeight = '90%';
            zoomedImg.style.objectFit = 'contain';
            
            modal.appendChild(zoomedImg);
            document.body.appendChild(modal);
            
            // クリックで閉じる
            modal.addEventListener('click', () => {
                document.body.removeChild(modal);
            });
        });
    });
} 