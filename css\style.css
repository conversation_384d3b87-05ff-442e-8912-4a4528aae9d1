:root {
    --primary-color: #007AFF;
    --secondary-color: #5856D6;
    --background-color: #ffffff;
    --text-color: #000000;
    --card-shadow: 0 8px 16px rgba(0,0,0,0.1);
    --hover-transition: all 0.3s ease;
    --border-radius: 12px;
}

/* ダークモード */
[data-theme="dark"] {
    --background-color: #000000;
    --text-color: #ffffff;
    --card-shadow: 0 8px 16px rgba(255,255,255,0.05);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    transition: background-color 0.3s ease;
    line-height: 1.6;
}

/* ヘッダースタイルの改善 */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
}

h1 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
}

.lead {
    font-size: 1.2rem;
    color: var(--text-color);
    opacity: 0.8;
}

/* カード共通スタイル */
.card {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--card-shadow);
    transition: var(--hover-transition);
    background-color: var(--background-color);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0,0,0,0.15);
}

.calculator-form {
    max-width: 600px;
    margin: 0 auto;
    overflow: hidden;
}

/* フォーム要素の改善 */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid rgba(0,0,0,0.1);
    padding: 0.75rem 1rem;
    transition: var(--hover-transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0,122,255,0.25);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    transition: var(--hover-transition);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,122,255,0.3);
}

/* ナビゲーションバーの改善 */
.navbar {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.9) !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.3rem;
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 6px;
    transition: var(--hover-transition);
}

.nav-link:hover {
    background-color: rgba(0,0,0,0.05);
}

.nav-link.active {
    color: var(--primary-color) !important;
    background-color: rgba(0,122,255,0.1);
}

/* 結果表示の改善 */
#result {
    max-width: 600px;
    margin: 20px auto;
    padding: 20px;
    border-radius: var(--border-radius);
    background-color: rgba(var(--primary-color), 0.1);
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* フッターの改善 */
.footer {
    background-color: var(--background-color);
    border-top: 1px solid rgba(0,0,0,0.1);
    padding: 2rem 0;
}

.footer a {
    color: var(--text-color);
    opacity: 0.8;
    transition: var(--hover-transition);
}

.footer a:hover {
    opacity: 1;
    text-decoration: none;
}

/* レスポンシブデザイン */
@media (max-width: 768px) {
    .container {
        padding: 0 20px;
    }
    
    h1 {
        font-size: 1.8rem;
    }
}

/* アニメーション */
.calculator-form {
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.calculator-form:hover {
    transform: translateY(-2px);
}

/* 月間計算フォームのスタイル */
.result-details .row {
    margin: -10px;
}

.result-details .col-md-6 {
    padding: 10px;
}

.result-details h4 {
    color: var(--primary-color);
    border-bottom: 2px solid rgba(0,0,0,0.1);
    padding-bottom: 5px;
}

/* アニメーション効果 */
.alert {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* モバイル対応の改善 */
@media (max-width: 576px) {
    .result-details .row {
        margin: -5px;
    }
    
    .result-details .col-md-6 {
        padding: 5px;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: 4px !important;
        margin: 2px 0;
    }
}

/* モーダルのスタイル */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 2px solid rgba(0,0,0,0.1);
    background-color: var(--background-color);
}

.modal-body {
    padding: 20px;
    background-color: var(--background-color);
}

/* グラフのスタイル */
#monthlyChart {
    min-height: 300px;
}

/* 通知のスタイル */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    min-width: 250px;
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ダークモード対応 */
[data-theme="dark"] .modal-content {
    background-color: var(--background-color);
    color: var(--text-color);
}

[data-theme="dark"] .modal-header {
    border-bottom-color: rgba(255,255,255,0.1);
} 

/* パンくずリストのスタイル */
.breadcrumb {
    background-color: transparent;
    padding: 0.5rem 0;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: var(--text-color);
}

/* ダークモード対応 */
[data-theme="dark"] .breadcrumb-item a {
    color: #5b9dd9;
}

[data-theme="dark"] .breadcrumb-item.active {
    color: var(--text-color);
}

[data-theme="dark"] .breadcrumb-item+.breadcrumb-item::before {
    color: var(--text-color);
} 

@media (max-width: 576px) {
    .calculator-form {
        padding: 15px;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin: 2px 0;
        width: 100%;
    }
    
    .nav-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .nav-tabs .nav-link {
        white-space: nowrap;
    }
}

/* 新しい計算エリアのスタイル - フレックスボックスレイアウト */
.calculator-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.calculator-box {
    display: flex;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    background-color: var(--background-color);
    margin-bottom: 30px;
    min-height: 500px;
}

.calculator-input {
    flex: 0 0 38%;
    min-width: 280px;
    position: relative;
    border-right: 1px solid rgba(0,0,0,0.1);
    background-color: rgba(0,122,255,0.03);
    transition: flex 0.3s ease;
    overflow-y: auto;
    max-height: 600px;
}

.calculator-result {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.handle-bar {
    width: 16px;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0,0,0,0.05);
    cursor: col-resize;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.handle {
    width: 4px;
    height: 40px;
    background-color: rgba(0,0,0,0.2);
    border-radius: 2px;
}

.handle-bar:hover {
    background-color: rgba(0,0,0,0.1);
}

.handle-bar:hover .handle {
    background-color: rgba(0,0,0,0.3);
}

.chart-container {
    flex: 1;
    margin-top: 20px;
    min-height: 250px;
    position: relative;
}

#resultDisplay, #monthlyResultDisplay, #highwayResultDisplay {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* ダークモードでの計算エリア対応 */
[data-theme="dark"] .calculator-input {
    background-color: rgba(0,122,255,0.05);
    border-right: 1px solid rgba(255,255,255,0.1);
}

[data-theme="dark"] .handle-bar {
    background-color: rgba(255,255,255,0.05);
}

[data-theme="dark"] .handle {
    background-color: rgba(255,255,255,0.2);
}

[data-theme="dark"] .handle-bar:hover {
    background-color: rgba(255,255,255,0.1);
}

[data-theme="dark"] .handle-bar:hover .handle {
    background-color: rgba(255,255,255,0.3);
}

/* 結果表示の改善 */
#resultData, #monthlyResultData, #highwayResultData {
    background-color: rgba(var(--primary-color), 0.05);
    padding: 15px;
    border-radius: 10px;
    margin-top: 10px;
}

/* レスポンシブデザイン対応 */
@media (max-width: 768px) {
    .calculator-box {
        flex-direction: column;
        min-height: auto;
    }
    
    .calculator-input {
        flex: 0 0 auto;
        width: 100%;
        max-height: none;
        border-right: none;
        border-bottom: 1px solid rgba(0,0,0,0.1);
    }
    
    .calculator-result {
        min-height: 400px;
    }
    
    .handle-bar {
        display: none;
    }
} 