<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ガソリン代計算 | 距離と燃費から簡単に交通費を計算</title>
    <meta name="description" content="ガソリン代計算が簡単にできるツール。軽自動車やSUVなど車種別の燃費データを活用し、走行距離からガソリン代を正確に算出。割り勘計算や地図連携も可能。">
    <meta name="keywords" content="ガソリン代計算, 交通費, 軽自動車, 燃費, 割り勘, 距離, ガソリン代 目安">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- Custom styles -->
    <style>
        body {
            font-family: 'Noto Sans JP', sans-serif;
        }
        .calculator-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }
        .calculator-form {
            flex: 1;
            min-width: 300px;
        }
        .chart-container {
            flex: 1;
            min-width: 300px;
            height: 400px;
        }
        .draggable-container {
            position: relative;
        }
        .draggable-slider {
            margin: 20px 0;
        }
        .fuel-type-cards {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        .fuel-type-card {
            flex: 1;
            cursor: pointer;
            transition: transform 0.2s;
            border: 2px solid #ddd;
        }
        .fuel-type-card.active {
            border-color: #0d6efd;
            transform: translateY(-5px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .price-trend-chart {
            height: 250px;
        }
        .faq-item {
            margin-bottom: 1rem;
        }
        .keyword-tag {
            display: inline-block;
            background: #f0f8ff;
            padding: 3px 10px;
            border-radius: 15px;
            margin: 5px;
            font-size: 0.85rem;
            color: #0d6efd;
        }
    </style>
<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8016227945839388"
     crossorigin="anonymous"></script>
</head>
<body>
    <!-- ナビゲーションバー -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">計算ツール</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="gas.html">ガソリン代計算</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="traffic.html">交通費計算</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fuel.html">燃費計算</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="blog.html">ブログ</a>
                    </li>
                </ul>
                <div class="dark-mode-toggle">
                    <button class="btn" id="darkModeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- メインコンテンツ -->
    <main class="container mt-5 pt-5">
        <!-- パンくずリスト -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">ホーム</a></li>
                <li class="breadcrumb-item active" aria-current="page">ガソリン代計算</li>
            </ol>
        </nav>

        <h1 class="text-center mb-3">ガソリン代計算ツール</h1>
        <p class="lead text-center mb-4">走行距離と燃費からガソリン代を正確に計算</p>
        
        <div class="keyword-badges text-center mb-4">
            <span class="keyword-tag">ガソリン代 計算</span>
            <span class="keyword-tag">ガソリン代 距離 目安</span>
            <span class="keyword-tag">軽自動車</span>
            <span class="keyword-tag">交通費</span>
            <span class="keyword-tag">割り勘</span>
        </div>

        <!-- 新しい計算機インターフェース -->
        <div class="calculator-container">
            <!-- 左側：入力フォーム（ドラッグ可能なスライダー付き） -->
            <div class="calculator-form card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h3 class="h5 m-0">ガソリン代計算入力</h3>
                </div>
                <div class="card-body">
                    <!-- 車種選択 -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">車種選択</label>
                        <select class="form-select" id="carType">
                            <option value="custom">手動入力</option>
                            <option value="compact">軽自動車 (平均燃費: 25km/L)</option>
                            <option value="sedan">セダン (平均燃費: 15km/L)</option>
                            <option value="suv">SUV (平均燃費: 12km/L)</option>
                            <option value="hybrid">ハイブリッド車 (平均燃費: 30km/L)</option>
                        </select>
                    </div>

                    <!-- 燃料タイプカード -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">燃料タイプ</label>
                        <div class="fuel-type-cards">
                            <div class="fuel-type-card card text-center active" data-fuel-type="regular" data-price="160">
                                <div class="card-body p-2">
                                    <h4 class="h6">レギュラー</h4>
                                    <p class="mb-0">全国平均: 160円/L</p>
                                </div>
                            </div>
                            <div class="fuel-type-card card text-center" data-fuel-type="premium" data-price="180">
                                <div class="card-body p-2">
                                    <h4 class="h6">ハイオク</h4>
                                    <p class="mb-0">全国平均: 180円/L</p>
                                </div>
                            </div>
                            <div class="fuel-type-card card text-center" data-fuel-type="diesel" data-price="145">
                                <div class="card-body p-2">
                                    <h4 class="h6">軽油</h4>
                                    <p class="mb-0">全国平均: 145円/L</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ガソリン価格 - スライダー付き -->
                    <div class="mb-3">
                        <label for="gasPrice" class="form-label fw-bold">ガソリン価格 (円/L): <span id="gasPriceValue">160</span>円</label>
                        <div class="draggable-slider">
                            <div id="gasPriceSlider"></div>
                        </div>
                        <input type="hidden" id="gasPrice" value="160">
                        <div class="d-flex justify-content-between">
                            <small>120円</small>
                            <small>200円</small>
                        </div>
                    </div>

                    <!-- 走行距離 - スライダー付き -->
                    <div class="mb-3">
                        <label for="distance" class="form-label fw-bold">走行距離 (km): <span id="distanceValue">100</span>km</label>
                        <div class="draggable-slider">
                            <div id="distanceSlider"></div>
                        </div>
                        <input type="hidden" id="distance" value="100">
                        <div class="d-flex justify-content-between">
                            <small>10km</small>
                            <small>1000km</small>
                        </div>
                    </div>

                    <!-- 燃費 - スライダー付き -->
                    <div class="mb-3">
                        <label for="fuelEfficiency" class="form-label fw-bold">燃費 (km/L): <span id="fuelEfficiencyValue">15</span>km/L</label>
                        <div class="draggable-slider">
                            <div id="fuelEfficiencySlider"></div>
                        </div>
                        <input type="hidden" id="fuelEfficiency" value="15">
                        <div class="d-flex justify-content-between">
                            <small>5km/L</small>
                            <small>35km/L</small>
                        </div>
                    </div>

                    <!-- 追加オプション -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">追加オプション</label>
                        <div class="form-check mb-2">
                            <input type="checkbox" class="form-check-input" id="roundTrip">
                            <label class="form-check-label" for="roundTrip">往復で計算</label>
                        </div>
                        <div class="form-check mb-2">
                            <input type="checkbox" class="form-check-input" id="splitCost">
                            <label class="form-check-label" for="splitCost">割り勘で計算</label>
                        </div>
                        <div class="input-group mb-2" id="peopleCountGroup" style="display:none;">
                            <span class="input-group-text">人数</span>
                            <input type="number" class="form-control" id="peopleCount" value="2" min="2" max="10">
                            <span class="input-group-text">人</span>
                        </div>
                    </div>

                    <button class="btn btn-primary w-100 mt-3" id="calculateBtn">
                        <i class="fas fa-calculator me-2"></i>ガソリン代を計算する
                    </button>
                </div>
            </div>

            <!-- 右側：計算結果とチャート表示 -->
            <div class="chart-container card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h3 class="h5 m-0">計算結果とグラフ</h3>
                </div>
                <div class="card-body">
                    <!-- 計算結果表示エリア -->
                    <div id="result" class="mb-4">
                        <div class="text-center py-5">
                            <p class="text-muted">左側の項目を入力して「計算する」ボタンを押してください</p>
                        </div>
                    </div>
                    
                    <!-- グラフ表示エリア -->
                    <div class="chart-area">
                        <canvas id="gasCostChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 計算履歴 -->
        <section class="mt-5">
            <h2 class="mb-4">計算履歴</h2>
            <div class="history-list card shadow-sm">
                <div class="card-body">
                    <div id="calculationHistory">
                        <!-- 履歴がJavaScriptで追加される -->
                        <p class="text-muted text-center">計算履歴がここに表示されます</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- ガソリン価格の傾向 -->
        <section class="mt-5">
            <h2 class="section-title mb-4">ガソリン価格の傾向</h2>
            <div class="card shadow-sm">
                <div class="card-body">
                    <p class="mb-4">全国のガソリン価格の推移を把握し、給油のタイミングを計画しましょう。最近の平均価格と動向から最適な給油計画を立てることができます。</p>
                    
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="price-trend-chart">
                                <canvas id="priceTrendChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="current-prices">
                                <h4 class="h6 mb-3">現在の全国平均価格（2023年12月時点）</h4>
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        レギュラー
                                        <span class="badge bg-primary rounded-pill">160円/L</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        ハイオク
                                        <span class="badge bg-primary rounded-pill">180円/L</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        軽油
                                        <span class="badge bg-primary rounded-pill">145円/L</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <h4 class="h5 mb-3">地域別ガソリン価格の違い</h4>
                    <p>地域によってガソリン価格には差があります。都市部は競争が激しく価格が安く、地方では輸送コストなどの影響で高めになる傾向があります。お住まいの地域の相場を把握しておくと便利です。</p>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>地域</th>
                                    <th>レギュラー</th>
                                    <th>ハイオク</th>
                                    <th>軽油</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>北海道</td>
                                    <td>163円/L</td>
                                    <td>183円/L</td>
                                    <td>148円/L</td>
                                </tr>
                                <tr>
                                    <td>東北</td>
                                    <td>162円/L</td>
                                    <td>182円/L</td>
                                    <td>147円/L</td>
                                </tr>
                                <tr>
                                    <td>関東</td>
                                    <td>158円/L</td>
                                    <td>178円/L</td>
                                    <td>143円/L</td>
                                </tr>
                                <tr>
                                    <td>中部</td>
                                    <td>159円/L</td>
                                    <td>179円/L</td>
                                    <td>144円/L</td>
                                </tr>
                                <tr>
                                    <td>関西</td>
                                    <td>157円/L</td>
                                    <td>177円/L</td>
                                    <td>142円/L</td>
                                </tr>
                                <tr>
                                    <td>中国・四国</td>
                                    <td>161円/L</td>
                                    <td>181円/L</td>
                                    <td>146円/L</td>
                                </tr>
                                <tr>
                                    <td>九州・沖縄</td>
                                    <td>164円/L</td>
                                    <td>184円/L</td>
                                    <td>149円/L</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </section>

        <!-- お役立ち情報 -->
        <section class="mt-5">
            <h2 class="section-title mb-4">ガソリン代を節約するコツ</h2>
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="info-card card h-100">
                        <div class="card-header bg-success text-white">
                            <h3 class="h5 m-0">燃費を良くするドライブテクニック</h3>
                        </div>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">✓ 急発進・急加速・急ブレーキを避ける（約10%の燃費向上）</li>
                                <li class="list-group-item">✓ タイヤの空気圧を適正に保つ（約3%の燃費向上）</li>
                                <li class="list-group-item">✓ 不要な荷物を積まない（10kgの軽量化で約1%の燃費向上）</li>
                                <li class="list-group-item">✓ アイドリングを控える（10分間で約160ccのガソリン消費）</li>
                                <li class="list-group-item">✓ エアコンの使用を適切に（約10%の燃費影響）</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-card card h-100">
                        <div class="card-header bg-info text-white">
                            <h3 class="h5 m-0">車種別燃費の特徴</h3>
                        </div>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <strong>軽自動車：</strong> 一般的に燃費が良く、維持費も安い。平均燃費は20〜28km/L程度。
                                </li>
                                <li class="list-group-item">
                                    <strong>セダン・コンパクトカー：</strong> バランスの取れた燃費性能で、平均15〜20km/L程度。
                                </li>
                                <li class="list-group-item">
                                    <strong>SUV・ミニバン：</strong> 車体が大きいため燃費は10〜15km/L程度と低め。
                                </li>
                                <li class="list-group-item">
                                    <strong>ハイブリッド車：</strong> 市街地走行では30km/L以上と非常に優れた燃費性能。
                                </li>
                                <li class="list-group-item">
                                    <strong>電気自動車：</strong> ガソリン代は不要だが、充電コストと走行距離の制限を考慮。
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="info-card card h-100">
                        <div class="card-header bg-warning">
                            <h3 class="h5 m-0">効率的な給油方法</h3>
                        </div>
                        <div class="card-body">
                            <p>効率的な給油方法を心がけることでガソリン代を節約できます：</p>
                            <ul>
                                <li>週の初めや値上げ前に給油する</li>
                                <li>セルフサービスのスタンドを利用する（フルサービスより5〜10円/L安い）</li>
                                <li>ガソリンスタンドのポイントカードや提携クレジットカードを活用する</li>
                                <li>ガソリン価格比較アプリを利用して最安値のスタンドを探す</li>
                                <li>タンクを空に近い状態まで減らさない（半分程度で給油するのが理想的）</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-card card h-100">
                        <div class="card-header bg-danger text-white">
                            <h3 class="h5 m-0">ガソリン代 計算と交通費精算のポイント</h3>
                        </div>
                        <div class="card-body">
                            <p>ビジネスや友人との旅行でガソリン代を割り勘する際のポイント：</p>
                            <ul>
                                <li>実際の燃費を把握しておく（走行距離÷給油量）</li>
                                <li>高速道路料金も含めた総コストで計算する</li>
                                <li>乗車人数で均等に分ける基本的な割り勘方法</li>
                                <li>ドライバーの負担（運転疲労など）も考慮するのがマナー</li>
                                <li>一般的には「ガソリン代＋高速道路料金」を人数で割るのが標準的</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- よくある質問 -->
        <section class="faq-section mt-5">
            <h2 class="section-title mb-4">よくある質問</h2>
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h3 class="accordion-header" id="faqHeading1">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse1">
                                    軽自動車のガソリン代はどのくらい安くなりますか？
                                </button>
                            </h3>
                            <div id="faqCollapse1" class="accordion-collapse collapse" aria-labelledby="faqHeading1" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <p>軽自動車は一般的に燃費が良いため、ガソリン代を節約できます。例えば、燃費が25km/Lの軽自動車と15km/Lのセダンを比較すると、100km走行する場合：</p>
                                    <ul>
                                        <li>軽自動車：100km ÷ 25km/L × 160円/L = 640円</li>
                                        <li>セダン：100km ÷ 15km/L × 160円/L = 1,067円</li>
                                    </ul>
                                    <p>この場合、軽自動車は約40%ガソリン代を節約できます。ただし、車種や走行条件によって実際の燃費は変わります。</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h3 class="accordion-header" id="faqHeading2">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse2">
                                    ガソリン代の目安はどのように計算すればいいですか？
                                </button>
                            </h3>
                            <div id="faqCollapse2" class="accordion-collapse collapse" aria-labelledby="faqHeading2" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <p>ガソリン代の基本的な計算式は以下の通りです：</p>
                                    <div class="alert alert-info">
                                        ガソリン代 = 走行距離(km) ÷ 燃費(km/L) × ガソリン価格(円/L)
                                    </div>
                                    <p>例えば、燃費が15km/Lの車で200km走行し、ガソリン価格が160円/Lの場合：</p>
                                    <p>200km ÷ 15km/L × 160円/L = 2,133円</p>
                                    <p>さらに正確な計算には、走行条件（市街地や高速道路）や空調の使用なども考慮すると良いでしょう。</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h3 class="accordion-header" id="faqHeading3">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse3">
                                    友人との旅行でガソリン代を割り勘するときのマナーは？
                                </button>
                            </h3>
                            <div id="faqCollapse3" class="accordion-collapse collapse" aria-labelledby="faqHeading3" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <p>友人との旅行でガソリン代を割り勘する際のポイントは以下の通りです：</p>
                                    <ol>
                                        <li>事前に割り勘方法を決めておく（均等割り、ドライバー割引など）</li>
                                        <li>高速道路料金や駐車場代も含めるかどうか確認する</li>
                                        <li>実際にかかった費用を明確にする（レシートを保管するなど）</li>
                                        <li>ドライバーの負担（運転疲労など）も考慮するのがマナー</li>
                                        <li>一般的には「ガソリン代＋高速道路料金」を人数で割るのが標準的</li>
                                    </ol>
                                    <p>当サイトの割り勘計算機能を使えば、人数に応じた金額を簡単に算出できます。</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h3 class="accordion-header" id="faqHeading4">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse4">
                                    ガソリン代と電車やバスなどの公共交通機関の料金を比較するには？
                                </button>
                            </h3>
                            <div id="faqCollapse4" class="accordion-collapse collapse" aria-labelledby="faqHeading4" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <p>ガソリン代と公共交通機関の料金を比較する場合は、以下の点を考慮しましょう：</p>
                                    <ul>
                                        <li>車の場合は、ガソリン代だけでなく、高速道路料金や駐車場代も含める</li>
                                        <li>乗車人数が増えると、車の方が一人あたりのコストが下がる</li>
                                        <li>長距離移動では、高速バスや新幹線との比較も重要</li>
                                        <li>時間価値（移動時間の差）も考慮する</li>
                                    </ul>
                                    <p>当サイトのガソリン代計算ツールと交通費計算ツールを併用すれば、より正確な比較ができます。</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h3 class="accordion-header" id="faqHeading5">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse5">
                                    アプリやエクセルでガソリン代計算するメリットは？
                                </button>
                            </h3>
                            <div id="faqCollapse5" class="accordion-collapse collapse" aria-labelledby="faqHeading5" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <p>ガソリン代計算ツールを使用するメリットは多岐にわたります：</p>
                                    <ul>
                                        <li>正確で素早い計算が可能（手計算よりも誤差が少ない）</li>
                                        <li>複数のシナリオを比較できる（異なるルートや車種など）</li>
                                        <li>履歴機能で過去の計算を参照できる</li>
                                        <li>分かりやすいグラフ表示でコスト傾向を把握できる</li>
                                        <li>割り勘計算も簡単に行える</li>
                                    </ul>
                                    <p>当サイトのツールは、ブラウザ上で動作するため、インストール不要で簡単に利用できます。また、スマートフォンにも対応しているため、外出先でも便利にご利用いただけます。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- フッター -->
    <footer class="footer mt-5 py-3 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <h5>ガソリン代計算ツール</h5>
                    <p class="text-muted">走行距離と燃費からガソリン代を簡単に計算できるツールです。軽自動車やSUVなど、様々な車種に対応しています。</p>
                </div>
                <div class="col-md-4 mb-3">
                    <h5>関連ツール</h5>
                    <ul class="list-unstyled">
                        <li><a href="traffic.html" class="text-decoration-none">交通費計算</a></li>
                        <li><a href="fuel.html" class="text-decoration-none">燃費計算</a></li>
                    </ul>
                </div>
                <div class="col-md-4 mb-3">
                    <h5>お役立ちリンク</h5>
                    <ul class="list-unstyled">
                        <li><a href="blog.html" class="text-decoration-none">ドライブお役立ちブログ</a></li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <div class="mb-3">
                    <a href="terms.html" class="text-muted mx-2">利用規約</a>
                    <a href="contact.html" class="text-muted mx-2">お問い合わせ</a>
                    <a href="privacy.html" class="text-muted mx-2">プライバシーポリシー</a>
                </div>
                <p class="text-muted">©2025 ガソリン代計算ツール</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-kit-code.js"></script>
    <script>
        // ドキュメントの読み込み完了時に実行
        document.addEventListener('DOMContentLoaded', function() {
            // スライダーの初期化
            initializeSliders();
            
            // 燃料タイプカードのクリックイベント
            initializeFuelTypeCards();
            
            // 車種選択の変更イベント
            document.getElementById('carType').addEventListener('change', function() {
                updateFuelEfficiency(this.value);
            });
            
            // 割り勘チェックボックスのイベント
            document.getElementById('splitCost').addEventListener('change', function() {
                document.getElementById('peopleCountGroup').style.display = this.checked ? 'flex' : 'none';
            });
            
            // 計算ボタンのクリックイベント
            document.getElementById('calculateBtn').addEventListener('click', calculateGas);
            
            // 価格トレンドチャートの初期化
            initializePriceTrendChart();
            
            // 初期のガソリンコストチャートを表示
            initializeGasCostChart();
        });

        // スライダーの初期化関数
        function initializeSliders() {
            // ガソリン価格スライダー
            $("#gasPriceSlider").slider({
                range: "min",
                min: 120,
                max: 200,
                value: 160,
                slide: function(event, ui) {
                    $("#gasPriceValue").text(ui.value);
                    $("#gasPrice").val(ui.value);
                    updateGasCostChart();
                }
            });
            
            // 走行距離スライダー
            $("#distanceSlider").slider({
                range: "min",
                min: 10,
                max: 1000,
                value: 100,
                slide: function(event, ui) {
                    $("#distanceValue").text(ui.value);
                    $("#distance").val(ui.value);
                    updateGasCostChart();
                }
            });
            
            // 燃費スライダー
            $("#fuelEfficiencySlider").slider({
                range: "min",
                min: 5,
                max: 35,
                value: 15,
                slide: function(event, ui) {
                    $("#fuelEfficiencyValue").text(ui.value);
                    $("#fuelEfficiency").val(ui.value);
                    updateGasCostChart();
                }
            });
        }

        // 燃料タイプカードの初期化
        function initializeFuelTypeCards() {
            document.querySelectorAll('.fuel-type-card').forEach(card => {
                card.addEventListener('click', function() {
                    // アクティブクラスを全カードから削除
                    document.querySelectorAll('.fuel-type-card').forEach(c => {
                        c.classList.remove('active');
                    });
                    
                    // クリックされたカードにアクティブクラスを追加
                    this.classList.add('active');
                    
                    // ガソリン価格を更新
                    const price = this.getAttribute('data-price');
                    $("#gasPriceValue").text(price);
                    $("#gasPrice").val(price);
                    $("#gasPriceSlider").slider("value", price);
                    
                    // チャートを更新
                    updateGasCostChart();
                });
            });
        }

        // 車種に基づいて燃費を更新
        function updateFuelEfficiency(carType) {
            let fuelEfficiency = 15; // デフォルト値
            
            switch(carType) {
                case 'compact': // 軽自動車
                    fuelEfficiency = 25;
                    break;
                case 'sedan': // セダン
                    fuelEfficiency = 15;
                    break;
                case 'suv': // SUV
                    fuelEfficiency = 12;
                    break;
                case 'hybrid': // ハイブリッド
                    fuelEfficiency = 30;
                    break;
            }
            
            // 燃費の値を更新
            $("#fuelEfficiencyValue").text(fuelEfficiency);
            $("#fuelEfficiency").val(fuelEfficiency);
            $("#fuelEfficiencySlider").slider("value", fuelEfficiency);
            
            // チャートを更新
            updateGasCostChart();
        }

        // ガソリン代計算関数
        function calculateGas() {
            // 入力値の取得
            const distance = parseFloat(document.getElementById('distance').value);
            const fuelEfficiency = parseFloat(document.getElementById('fuelEfficiency').value);
            const gasPrice = parseFloat(document.getElementById('gasPrice').value);
            const isRoundTrip = document.getElementById('roundTrip').checked;
            const isSplitCost = document.getElementById('splitCost').checked;
            const peopleCount = isSplitCost ? parseInt(document.getElementById('peopleCount').value) : 1;
            
            // 有効な入力かチェック
            if (isNaN(distance) || isNaN(fuelEfficiency) || isNaN(gasPrice)) {
                document.getElementById('result').innerHTML = '<div class="alert alert-danger">有効な数値を入力してください</div>';
                return;
            }
            
            // 計算
            let totalDistance = isRoundTrip ? distance * 2 : distance;
            let fuelAmount = totalDistance / fuelEfficiency;
            let cost = fuelAmount * gasPrice;
            let costPerPerson = cost / peopleCount;
            
            // 結果を表示
            let resultHTML = `
                <div class="alert alert-success mb-4">
                    <h3 class="alert-heading">計算結果</h3>
                    <hr>
                    <div class="row">
                        <div class="col-sm-6">
                            <p class="mb-1"><strong>総走行距離:</strong> ${totalDistance.toFixed(1)} km ${isRoundTrip ? '(往復)' : '(片道)'}</p>
                            <p class="mb-1"><strong>使用燃料:</strong> ${fuelAmount.toFixed(2)} L</p>
                            <p class="mb-1"><strong>ガソリン単価:</strong> ${gasPrice} 円/L</p>
                        </div>
                        <div class="col-sm-6">
                            <p class="mb-1"><strong>総ガソリン代:</strong> ${cost.toFixed(0)} 円</p>
                            ${isSplitCost ? `<p class="mb-1"><strong>1人あたり:</strong> ${costPerPerson.toFixed(0)} 円 (${peopleCount}人で割り勘)</p>` : ''}
                            <p class="mb-1"><strong>1km あたり:</strong> ${(cost / totalDistance).toFixed(1)} 円/km</p>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('result').innerHTML = resultHTML;
            document.getElementById('result').classList.remove('d-none');
            
            // 計算履歴に追加
            addToHistory({
                distance: totalDistance,
                fuelEfficiency: fuelEfficiency,
                gasPrice: gasPrice,
                cost: cost,
                isSplitCost: isSplitCost,
                peopleCount: peopleCount,
                costPerPerson: costPerPerson,
                timestamp: new Date()
            });
            
            // チャートを更新
            updateGasCostChart();
        }

        // 計算履歴に追加する関数
        function addToHistory(data) {
            const historyContainer = document.getElementById('calculationHistory');
            
            // 最初の「履歴がここに表示されます」テキストを削除
            if (historyContainer.querySelector('.text-muted')) {
                historyContainer.innerHTML = '';
            }
            
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item border-bottom pb-2 mb-2';
            
            const dateOptions = { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
            const formattedDate = data.timestamp.toLocaleDateString('ja-JP', dateOptions);
            
            historyItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <span class="fw-bold">${data.distance}km</span>
                        <small class="text-muted">(${data.fuelEfficiency}km/L, ${data.gasPrice}円/L)</small>
                    </div>
                    <div class="text-end">
                        <span class="fw-bold">${data.cost.toFixed(0)}円</span>
                        ${data.isSplitCost ? `<small class="text-muted">(1人: ${data.costPerPerson.toFixed(0)}円)</small>` : ''}
                        <br>
                        <small class="text-muted">${formattedDate}</small>
                    </div>
                </div>
            `;
            
            // 履歴の先頭に追加
            historyContainer.insertBefore(historyItem, historyContainer.firstChild);
            
            // ローカルストレージに保存（オプション）
            saveHistoryToLocalStorage();
        }

        // ローカルストレージに履歴を保存する関数
        function saveHistoryToLocalStorage() {
            // 実際のアプリケーションでは履歴をローカルストレージに保存する実装をここに追加
        }

        // ガソリン価格トレンドチャートの初期化
        function initializePriceTrendChart() {
            // デモデータ
            const ctx = document.getElementById('priceTrendChart').getContext('2d');
            
            // 過去6ヶ月のデータ（月ごと）
            const labels = ['7月', '8月', '9月', '10月', '11月', '12月'];
            const regularData = [165, 170, 168, 163, 158, 160];
            const premiumData = [185, 190, 188, 183, 178, 180];
            const dieselData = [150, 155, 153, 148, 143, 145];
            
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'レギュラー',
                            data: regularData,
                            borderColor: 'rgba(54, 162, 235, 1)',
                            backgroundColor: 'rgba(54, 162, 235, 0.1)',
                            tension: 0.2,
                            fill: true
                        },
                        {
                            label: 'ハイオク',
                            data: premiumData,
                            borderColor: 'rgba(255, 99, 132, 1)',
                            backgroundColor: 'rgba(255, 99, 132, 0.1)',
                            tension: 0.2,
                            fill: true
                        },
                        {
                            label: '軽油',
                            data: dieselData,
                            borderColor: 'rgba(75, 192, 192, 1)',
                            backgroundColor: 'rgba(75, 192, 192, 0.1)',
                            tension: 0.2,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: '全国平均ガソリン価格の推移 (円/L)'
                        }
                    },
                    scales: {
                        y: {
                            min: 120,
                            title: {
                                display: true,
                                text: '価格 (円/L)'
                            }
                        }
                    }
                }
            });
        }

        // ガソリンコストチャートの初期化
        let gasCostChart;
        function initializeGasCostChart() {
            const ctx = document.getElementById('gasCostChart').getContext('2d');
            
            gasCostChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['片道コスト', '往復コスト'],
                    datasets: [{
                        label: 'ガソリン代 (円)',
                        data: [0, 0],
                        backgroundColor: [
                            'rgba(54, 162, 235, 0.5)',
                            'rgba(255, 99, 132, 0.5)'
                        ],
                        borderColor: [
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 99, 132, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: '計算結果のガソリン代'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'コスト (円)'
                            }
                        }
                    }
                }
            });
        }

        // チャートの更新関数
        function updateGasCostChart() {
            const distance = parseFloat(document.getElementById('distance').value) || 0;
            const fuelEfficiency = parseFloat(document.getElementById('fuelEfficiency').value) || 1;
            const gasPrice = parseFloat(document.getElementById('gasPrice').value) || 0;
            
            const oneWayCost = (distance / fuelEfficiency) * gasPrice;
            const roundTripCost = oneWayCost * 2;
            
            gasCostChart.data.datasets[0].data = [oneWayCost, roundTripCost];
            gasCostChart.update();
        }
    </script>
</body>
</html> 