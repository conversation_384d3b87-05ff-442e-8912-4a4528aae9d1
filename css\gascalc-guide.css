/* ガソリン代計算ガイドのスタイル */

/* 記事ヘッダー */
.blog-header {
    padding: 2rem 0;
    margin-bottom: 2rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
}

/* キーワードタグ */
.keyword-tag {
    display: inline-block;
    background: #f0f8ff;
    padding: 3px 10px;
    border-radius: 15px;
    margin: 5px;
    font-size: 0.85rem;
    color: #0d6efd;
    transition: all 0.3s ease;
}

.keyword-tag:hover {
    background: #e1efff;
    transform: translateY(-2px);
}

/* 記事メタ情報 */
.article-meta {
    color: #6c757d;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

/* 目次 */
.toc-container {
    border-left: 4px solid #0d6efd;
}

.toc-container h2 {
    font-size: 1.5rem;
    margin-top: 0;
}

.toc-container ul {
    margin-bottom: 0;
    padding-left: 1.2rem;
}

.toc-container a {
    color: #495057;
    text-decoration: none;
    transition: color 0.2s;
}

.toc-container a:hover {
    color: #0d6efd;
    text-decoration: underline;
}

/* 記事コンテンツ */
.blog-content {
    font-size: 1.05rem;
    line-height: 1.7;
}

.blog-content h2 {
    margin-top: 2.5rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #f0f0f0;
    font-weight: 700;
}

.blog-content h3 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.blog-content p {
    margin-bottom: 1.5rem;
}

.blog-content ul, .blog-content ol {
    margin-bottom: 1.5rem;
}

.blog-content img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 1.5rem 0;
}

/* 引用スタイル */
.blog-content blockquote {
    background-color: #f8f9fa;
    border-left: 4px solid #0d6efd;
    padding: 1rem 1.5rem;
    margin: 1.5rem 0;
    font-style: italic;
}

/* サイドバー */
.sidebar-box {
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    transition: transform 0.3s ease;
}

.sidebar-box:hover {
    transform: translateY(-5px);
}

.sidebar-box h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #f0f0f0;
}

.sidebar-box a {
    color: #0d6efd;
    text-decoration: none;
}

.sidebar-box a:hover {
    text-decoration: underline;
}

/* 著者情報 */
.author-avatar {
    color: #0d6efd;
}

/* 表のスタイル */
.calculation-table {
    width: 100%;
    margin-bottom: 1.5rem;
}

.calculation-table th {
    background-color: #f8f9fa;
}

/* フォーミュラボックス */
.formula-box {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 1.5rem 0;
    text-align: center;
    font-weight: 500;
}

/* ヒントボックス */
.tip-box {
    background-color: #e8f4ff;
    border-left: 4px solid #0d6efd;
    padding: 1rem;
    border-radius: 0 0.5rem 0.5rem 0;
    margin: 1.5rem 0;
}

.tip-box-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #0d6efd;
}

/* 注意ボックス */
.warning-box {
    background-color: #fff8e8;
    border-left: 4px solid #ffc107;
    padding: 1rem;
    border-radius: 0 0.5rem 0.5rem 0;
    margin: 1.5rem 0;
}

.warning-box-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #dc3545;
}

/* 計算例ボックス */
.example-box {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.5rem;
    margin: 1.5rem 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.example-box h4 {
    margin-top: 0;
    margin-bottom: 1rem;
    color: #0d6efd;
}

/* 画像キャプション */
.image-caption {
    text-align: center;
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: -1rem;
    margin-bottom: 1.5rem;
}

/* レスポンシブ調整 */
@media (max-width: 768px) {
    .blog-header {
        padding: 1.5rem 0;
    }
    
    .sidebar {
        margin-top: 3rem;
    }
}

/* ダークモード対応 */
body.dark-mode .blog-header,
body.dark-mode .toc-container,
body.dark-mode .sidebar-box,
body.dark-mode .example-box,
body.dark-mode .formula-box,
body.dark-mode blockquote,
body.dark-mode .calculation-table th {
    background-color: #2b3035;
}

body.dark-mode .keyword-tag {
    background-color: #212529;
    color: #8bb9fe;
}

body.dark-mode .keyword-tag:hover {
    background-color: #2c3238;
}

body.dark-mode .toc-container a {
    color: #ced4da;
}

body.dark-mode .toc-container a:hover {
    color: #8bb9fe;
}

body.dark-mode .tip-box {
    background-color: #233242;
}

body.dark-mode .warning-box {
    background-color: #332b24;
} 